#!/usr/bin/env python3
"""
ZARA Advanced Task Automation Framework
Created by: <PERSON>nam Sanjay

This module provides <PERSON><PERSON> with intelligent task automation capabilities:
- Learning from user behavior patterns
- Adaptive task execution with error recovery
- Complex multi-step workflow automation
- Context-aware task optimization
- Intelligent task scheduling and prioritization
- Self-improving automation through feedback
"""

import asyncio
import json
import logging
import time
import pyautogui
import pygetwindow as gw
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from collections import deque, defaultdict
import numpy as np

# Import our other systems
from advanced_visual_perception import visual_perception, VisualScene, VisualElement, VisualElementType
from autonomous_decision_engine import decision_engine, Decision, DecisionType
from multimodal_sensor_system import sensor_system, EnvironmentalContext

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskType(Enum):
    """Types of tasks that can be automated"""
    FORM_FILLING = "form_filling"
    DATA_ENTRY = "data_entry"
    FILE_MANAGEMENT = "file_management"
    WEB_NAVIGATION = "web_navigation"
    APPLICATION_CONTROL = "application_control"
    SYSTEM_MAINTENANCE = "system_maintenance"
    COMMUNICATION = "communication"
    CONTENT_CREATION = "content_creation"
    CUSTOM = "custom"

class TaskStatus(Enum):
    """Status of task execution"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ActionType(Enum):
    """Types of actions within tasks"""
    CLICK = "click"
    TYPE = "type"
    KEY_PRESS = "key_press"
    WAIT = "wait"
    VERIFY = "verify"
    NAVIGATE = "navigate"
    EXTRACT = "extract"
    CONDITION = "condition"
    LOOP = "loop"
    CUSTOM_FUNCTION = "custom_function"

@dataclass
class TaskAction:
    """Individual action within a task"""
    action_id: str
    action_type: ActionType
    parameters: Dict[str, Any]
    description: str
    timeout: float = 10.0
    retry_count: int = 3
    success_criteria: Optional[Dict[str, Any]] = None
    error_handling: Optional[Dict[str, Any]] = None

@dataclass
class AutomationTask:
    """Complete automation task definition"""
    task_id: str
    name: str
    description: str
    task_type: TaskType
    actions: List[TaskAction]
    prerequisites: List[str]
    success_criteria: Dict[str, Any]
    estimated_duration: timedelta
    created_at: datetime
    priority: int = 5  # 1-10 scale
    last_executed: Optional[datetime] = None
    success_rate: float = 0.0
    execution_count: int = 0
    learned_optimizations: List[Dict[str, Any]] = None

@dataclass
class TaskExecution:
    """Record of task execution"""
    execution_id: str
    task_id: str
    start_time: datetime
    end_time: Optional[datetime]
    status: TaskStatus
    actions_completed: int
    total_actions: int
    errors: List[str]
    performance_metrics: Dict[str, Any]
    context_snapshot: Optional[Dict[str, Any]]

class TaskLearningEngine:
    """Engine for learning and optimizing task execution"""
    
    def __init__(self):
        self.execution_history: deque = deque(maxlen=1000)
        self.pattern_database: Dict[str, Any] = {}
        self.optimization_rules: List[Dict[str, Any]] = []
        
    async def learn_from_execution(self, execution: TaskExecution, task: AutomationTask):
        """Learn from task execution to improve future performance"""
        try:
            # Store execution in history
            self.execution_history.append(execution)
            
            # Analyze execution patterns
            await self._analyze_execution_patterns(execution, task)
            
            # Update task success rate
            await self._update_task_metrics(execution, task)
            
            # Generate optimization suggestions
            optimizations = await self._generate_optimizations(execution, task)
            
            if optimizations:
                if task.learned_optimizations is None:
                    task.learned_optimizations = []
                task.learned_optimizations.extend(optimizations)
            
        except Exception as e:
            logger.error(f"❌ Error learning from execution: {e}")
    
    async def _analyze_execution_patterns(self, execution: TaskExecution, task: AutomationTask):
        """Analyze patterns in task execution"""
        try:
            pattern_key = f"{task.task_type.value}_{task.name}"
            
            if pattern_key not in self.pattern_database:
                self.pattern_database[pattern_key] = {
                    'execution_times': [],
                    'common_errors': defaultdict(int),
                    'success_contexts': [],
                    'failure_contexts': []
                }
            
            patterns = self.pattern_database[pattern_key]
            
            # Record execution time
            if execution.end_time:
                duration = (execution.end_time - execution.start_time).total_seconds()
                patterns['execution_times'].append(duration)
            
            # Record errors
            for error in execution.errors:
                patterns['common_errors'][error] += 1
            
            # Record context
            if execution.context_snapshot:
                if execution.status == TaskStatus.COMPLETED:
                    patterns['success_contexts'].append(execution.context_snapshot)
                else:
                    patterns['failure_contexts'].append(execution.context_snapshot)
            
        except Exception as e:
            logger.error(f"❌ Error analyzing execution patterns: {e}")
    
    async def _update_task_metrics(self, execution: TaskExecution, task: AutomationTask):
        """Update task performance metrics"""
        try:
            task.execution_count += 1
            task.last_executed = execution.start_time
            
            # Update success rate
            if execution.status == TaskStatus.COMPLETED:
                # Exponential moving average for success rate
                alpha = 0.1
                task.success_rate = (1 - alpha) * task.success_rate + alpha * 1.0
            else:
                task.success_rate = (1 - alpha) * task.success_rate + alpha * 0.0
            
        except Exception as e:
            logger.error(f"❌ Error updating task metrics: {e}")
    
    async def _generate_optimizations(self, execution: TaskExecution, task: AutomationTask) -> List[Dict[str, Any]]:
        """Generate optimization suggestions based on execution analysis"""
        try:
            optimizations = []
            
            # Timeout optimization
            if execution.performance_metrics.get('slow_actions'):
                optimizations.append({
                    'type': 'timeout_adjustment',
                    'description': 'Increase timeout for slow actions',
                    'parameters': {'timeout_multiplier': 1.5}
                })
            
            # Error recovery optimization
            if execution.errors:
                most_common_error = max(execution.errors, key=execution.errors.count)
                optimizations.append({
                    'type': 'error_recovery',
                    'description': f'Add recovery for common error: {most_common_error}',
                    'parameters': {'error_type': most_common_error}
                })
            
            # Context-based optimization
            if execution.context_snapshot:
                context = execution.context_snapshot
                if context.get('system_load') == 'high':
                    optimizations.append({
                        'type': 'performance_adjustment',
                        'description': 'Add delays for high system load',
                        'parameters': {'delay_multiplier': 1.3}
                    })
            
            return optimizations
            
        except Exception as e:
            logger.error(f"❌ Error generating optimizations: {e}")
            return []

class TaskExecutor:
    """Executes automation tasks with intelligent error handling"""
    
    def __init__(self):
        self.is_executing = False
        self.current_execution: Optional[TaskExecution] = None
        self.execution_queue: asyncio.Queue = asyncio.Queue()
        self.learning_engine = TaskLearningEngine()
        
    async def execute_task(self, task: AutomationTask, context: Optional[Dict[str, Any]] = None) -> TaskExecution:
        """Execute an automation task"""
        try:
            execution_id = f"exec_{task.task_id}_{int(time.time())}"
            
            execution = TaskExecution(
                execution_id=execution_id,
                task_id=task.task_id,
                start_time=datetime.now(),
                end_time=None,
                status=TaskStatus.RUNNING,
                actions_completed=0,
                total_actions=len(task.actions),
                errors=[],
                performance_metrics={},
                context_snapshot=context
            )
            
            self.current_execution = execution
            self.is_executing = True
            
            logger.info(f"🚀 Starting task execution: {task.name}")
            
            # Execute each action in sequence
            for i, action in enumerate(task.actions):
                try:
                    # Check if task should be paused/cancelled
                    if not self.is_executing:
                        execution.status = TaskStatus.CANCELLED
                        break
                    
                    # Execute action with retries
                    success = await self._execute_action_with_retry(action, task)
                    
                    if success:
                        execution.actions_completed += 1
                        logger.info(f"✅ Action {i+1}/{len(task.actions)} completed: {action.description}")
                    else:
                        error_msg = f"Action failed: {action.description}"
                        execution.errors.append(error_msg)
                        logger.error(f"❌ {error_msg}")
                        
                        # Check if task should continue or fail
                        if not await self._should_continue_after_error(action, task):
                            execution.status = TaskStatus.FAILED
                            break
                    
                except Exception as e:
                    error_msg = f"Exception in action {action.description}: {e}"
                    execution.errors.append(error_msg)
                    logger.error(f"❌ {error_msg}")
                    
                    if not await self._should_continue_after_error(action, task):
                        execution.status = TaskStatus.FAILED
                        break
            
            # Finalize execution
            execution.end_time = datetime.now()
            
            if execution.status == TaskStatus.RUNNING:
                if execution.actions_completed == execution.total_actions:
                    execution.status = TaskStatus.COMPLETED
                    logger.info(f"✅ Task completed successfully: {task.name}")
                else:
                    execution.status = TaskStatus.FAILED
                    logger.error(f"❌ Task failed: {task.name}")
            
            # Learn from execution
            await self.learning_engine.learn_from_execution(execution, task)
            
            self.is_executing = False
            self.current_execution = None
            
            return execution
            
        except Exception as e:
            logger.error(f"❌ Error executing task: {e}")
            execution.status = TaskStatus.FAILED
            execution.end_time = datetime.now()
            execution.errors.append(str(e))
            
            self.is_executing = False
            self.current_execution = None
            
            return execution

    async def _execute_action_with_retry(self, action: TaskAction, task: AutomationTask) -> bool:
        """Execute an action with retry logic"""
        try:
            for attempt in range(action.retry_count + 1):
                try:
                    success = await self._execute_single_action(action, task)

                    if success:
                        return True

                    if attempt < action.retry_count:
                        logger.warning(f"⚠️ Action failed, retrying ({attempt + 1}/{action.retry_count}): {action.description}")
                        await asyncio.sleep(1.0 * (attempt + 1))  # Exponential backoff

                except Exception as e:
                    if attempt < action.retry_count:
                        logger.warning(f"⚠️ Action exception, retrying ({attempt + 1}/{action.retry_count}): {e}")
                        await asyncio.sleep(1.0 * (attempt + 1))
                    else:
                        raise e

            return False

        except Exception as e:
            logger.error(f"❌ Error executing action with retry: {e}")
            return False

    async def _execute_single_action(self, action: TaskAction, task: AutomationTask) -> bool:
        """Execute a single action"""
        try:
            action_type = action.action_type
            params = action.parameters

            if action_type == ActionType.CLICK:
                return await self._execute_click_action(params)
            elif action_type == ActionType.TYPE:
                return await self._execute_type_action(params)
            elif action_type == ActionType.KEY_PRESS:
                return await self._execute_key_press_action(params)
            elif action_type == ActionType.WAIT:
                return await self._execute_wait_action(params)
            elif action_type == ActionType.VERIFY:
                return await self._execute_verify_action(params)
            elif action_type == ActionType.NAVIGATE:
                return await self._execute_navigate_action(params)
            elif action_type == ActionType.EXTRACT:
                return await self._execute_extract_action(params)
            elif action_type == ActionType.CONDITION:
                return await self._execute_condition_action(params)
            elif action_type == ActionType.CUSTOM_FUNCTION:
                return await self._execute_custom_function(params)
            else:
                logger.error(f"❌ Unknown action type: {action_type}")
                return False

        except Exception as e:
            logger.error(f"❌ Error executing single action: {e}")
            return False

    async def _execute_click_action(self, params: Dict[str, Any]) -> bool:
        """Execute click action"""
        try:
            # Get target element
            target = params.get('target')

            if isinstance(target, dict):
                # Find element by properties
                element = await self._find_element(target)
                if element:
                    x, y = element.center()
                    await asyncio.to_thread(pyautogui.click, x, y)
                    return True
            elif isinstance(target, tuple) and len(target) == 2:
                # Direct coordinates
                x, y = target
                await asyncio.to_thread(pyautogui.click, x, y)
                return True
            elif isinstance(target, str):
                # Find by text
                scene = visual_perception.current_scene
                if scene:
                    element = scene.find_element_by_text(target)
                    if element:
                        x, y = element.center()
                        await asyncio.to_thread(pyautogui.click, x, y)
                        return True

            return False

        except Exception as e:
            logger.error(f"❌ Click action error: {e}")
            return False

    async def _execute_type_action(self, params: Dict[str, Any]) -> bool:
        """Execute type action"""
        try:
            text = params.get('text', '')
            interval = params.get('interval', 0.05)

            await asyncio.to_thread(pyautogui.typewrite, text, interval)
            return True

        except Exception as e:
            logger.error(f"❌ Type action error: {e}")
            return False

    async def _execute_key_press_action(self, params: Dict[str, Any]) -> bool:
        """Execute key press action"""
        try:
            keys = params.get('keys')

            if isinstance(keys, str):
                await asyncio.to_thread(pyautogui.press, keys)
            elif isinstance(keys, list):
                await asyncio.to_thread(pyautogui.hotkey, *keys)

            return True

        except Exception as e:
            logger.error(f"❌ Key press action error: {e}")
            return False

    async def _execute_wait_action(self, params: Dict[str, Any]) -> bool:
        """Execute wait action"""
        try:
            duration = params.get('duration', 1.0)
            condition = params.get('condition')

            if condition:
                # Wait for condition
                timeout = params.get('timeout', 10.0)
                start_time = time.time()

                while time.time() - start_time < timeout:
                    if await self._check_condition(condition):
                        return True
                    await asyncio.sleep(0.5)

                return False
            else:
                # Simple wait
                await asyncio.sleep(duration)
                return True

        except Exception as e:
            logger.error(f"❌ Wait action error: {e}")
            return False

    async def _execute_verify_action(self, params: Dict[str, Any]) -> bool:
        """Execute verify action"""
        try:
            condition = params.get('condition')
            return await self._check_condition(condition) if condition else False

        except Exception as e:
            logger.error(f"❌ Verify action error: {e}")
            return False

    async def _execute_navigate_action(self, params: Dict[str, Any]) -> bool:
        """Execute navigate action"""
        try:
            url = params.get('url')
            application = params.get('application')

            if url:
                # Open URL in browser
                import webbrowser
                webbrowser.open(url)
                return True
            elif application:
                # Open application
                import subprocess
                subprocess.Popen(application)
                return True

            return False

        except Exception as e:
            logger.error(f"❌ Navigate action error: {e}")
            return False

    async def _execute_extract_action(self, params: Dict[str, Any]) -> bool:
        """Execute extract action"""
        try:
            target = params.get('target')
            variable = params.get('variable')

            # Extract text from element
            if target and variable:
                element = await self._find_element(target)
                if element and element.text:
                    # Store extracted value (simplified)
                    self._store_variable(variable, element.text)
                    return True

            return False

        except Exception as e:
            logger.error(f"❌ Extract action error: {e}")
            return False

    async def _execute_condition_action(self, params: Dict[str, Any]) -> bool:
        """Execute conditional action"""
        try:
            condition = params.get('condition')
            true_actions = params.get('true_actions', [])
            false_actions = params.get('false_actions', [])

            if await self._check_condition(condition):
                # Execute true actions
                for action_data in true_actions:
                    action = TaskAction(**action_data)
                    await self._execute_single_action(action, None)
            else:
                # Execute false actions
                for action_data in false_actions:
                    action = TaskAction(**action_data)
                    await self._execute_single_action(action, None)

            return True

        except Exception as e:
            logger.error(f"❌ Condition action error: {e}")
            return False

    async def _execute_custom_function(self, params: Dict[str, Any]) -> bool:
        """Execute custom function"""
        try:
            function_name = params.get('function')
            function_params = params.get('parameters', {})

            # This would call custom functions defined by the user
            # For now, just log the call
            logger.info(f"📞 Custom function call: {function_name} with {function_params}")
            return True

        except Exception as e:
            logger.error(f"❌ Custom function error: {e}")
            return False

    async def _find_element(self, target: Dict[str, Any]) -> Optional[VisualElement]:
        """Find element based on target criteria"""
        try:
            scene = visual_perception.current_scene
            if not scene:
                return None

            # Find by text
            if 'text' in target:
                return scene.find_element_by_text(target['text'])

            # Find by element type
            if 'type' in target:
                element_type = VisualElementType(target['type'])
                elements = scene.get_elements_by_type(element_type)
                if elements:
                    return elements[0]  # Return first match

            # Find by position
            if 'position' in target:
                x, y = target['position']
                for element in scene.elements:
                    ex, ey, ew, eh = element.bounds
                    if ex <= x <= ex + ew and ey <= y <= ey + eh:
                        return element

            return None

        except Exception as e:
            logger.error(f"❌ Error finding element: {e}")
            return None

    async def _check_condition(self, condition: Dict[str, Any]) -> bool:
        """Check if a condition is met"""
        try:
            condition_type = condition.get('type')

            if condition_type == 'element_exists':
                target = condition.get('target')
                element = await self._find_element(target)
                return element is not None

            elif condition_type == 'text_contains':
                text = condition.get('text')
                scene = visual_perception.current_scene
                if scene:
                    return text.lower() in scene.text_content.lower()

            elif condition_type == 'window_title':
                expected_title = condition.get('title')
                try:
                    active_window = gw.getActiveWindow()
                    if active_window:
                        return expected_title.lower() in active_window.title.lower()
                except:
                    pass

            return False

        except Exception as e:
            logger.error(f"❌ Error checking condition: {e}")
            return False

    def _store_variable(self, name: str, value: Any):
        """Store a variable for later use"""
        if not hasattr(self, 'variables'):
            self.variables = {}
        self.variables[name] = value

    def _get_variable(self, name: str) -> Any:
        """Get a stored variable"""
        if hasattr(self, 'variables'):
            return self.variables.get(name)
        return None

    async def _should_continue_after_error(self, action: TaskAction, task: AutomationTask) -> bool:
        """Determine if task should continue after an error"""
        try:
            # Check action-specific error handling
            if action.error_handling:
                continue_on_error = action.error_handling.get('continue', False)
                return continue_on_error

            # Default: stop on error for critical actions
            critical_actions = [ActionType.VERIFY, ActionType.CONDITION]
            return action.action_type not in critical_actions

        except Exception as e:
            return False

class AdvancedTaskAutomation:
    """Main automation framework"""

    def __init__(self):
        self.is_active = False
        self.tasks: Dict[str, AutomationTask] = {}
        self.executor = TaskExecutor()
        self.scheduler = TaskScheduler()
        self.task_builder = TaskBuilder()

        # Load saved tasks
        self._load_tasks()

    async def start_automation_system(self) -> str:
        """Start the automation system"""
        try:
            if self.is_active:
                return "🤖 Task Automation System already active!"

            self.is_active = True

            # Start scheduler
            asyncio.create_task(self.scheduler.start_scheduling())

            result = (
                f"🤖 **ADVANCED TASK AUTOMATION ACTIVATED!**\n"
                f"📋 Available Tasks: {len(self.tasks)}\n"
                f"⚡ Task Executor: READY\n"
                f"📅 Task Scheduler: ACTIVE\n"
                f"🧠 Learning Engine: ENABLED\n"
                f"🕐 Started: {datetime.now().strftime('%I:%M:%S %p')}"
            )

            logger.info("🤖 Advanced Task Automation System Started")
            return result

        except Exception as e:
            self.is_active = False
            logger.error(f"❌ Failed to start automation system: {e}")
            return f"❌ Failed to start automation system: {e}"

    async def stop_automation_system(self) -> str:
        """Stop the automation system"""
        try:
            self.is_active = False

            # Stop scheduler
            await self.scheduler.stop_scheduling()

            # Save tasks
            self._save_tasks()

            result = (
                f"🛑 **AUTOMATION SYSTEM STOPPED**\n"
                f"💾 Tasks Saved: {len(self.tasks)}\n"
                f"🕐 Stopped: {datetime.now().strftime('%I:%M:%S %p')}"
            )

            logger.info("🛑 Task Automation System Stopped")
            return result

        except Exception as e:
            logger.error(f"❌ Error stopping automation system: {e}")
            return f"❌ Error stopping automation system: {e}"

    def _load_tasks(self):
        """Load saved tasks from file"""
        try:
            with open('zara_automation_tasks.json', 'r') as f:
                tasks_data = json.load(f)

                for task_id, task_data in tasks_data.items():
                    # Convert action data back to TaskAction objects
                    actions = []
                    for action_data in task_data.get('actions', []):
                        action = TaskAction(
                            action_id=action_data['action_id'],
                            action_type=ActionType(action_data['action_type']),
                            parameters=action_data['parameters'],
                            description=action_data['description'],
                            timeout=action_data.get('timeout', 10.0),
                            retry_count=action_data.get('retry_count', 3)
                        )
                        actions.append(action)

                    # Create AutomationTask object
                    task = AutomationTask(
                        task_id=task_data['task_id'],
                        name=task_data['name'],
                        description=task_data['description'],
                        task_type=TaskType(task_data['task_type']),
                        actions=actions,
                        prerequisites=task_data.get('prerequisites', []),
                        success_criteria=task_data.get('success_criteria', {}),
                        estimated_duration=timedelta(seconds=task_data.get('estimated_duration', 60)),
                        priority=task_data.get('priority', 5),
                        created_at=datetime.fromisoformat(task_data['created_at']),
                        success_rate=task_data.get('success_rate', 0.0),
                        execution_count=task_data.get('execution_count', 0)
                    )

                    self.tasks[task_id] = task

        except FileNotFoundError:
            logger.info("📋 No saved tasks found, starting with empty task list")
        except Exception as e:
            logger.error(f"❌ Error loading tasks: {e}")

    def _save_tasks(self):
        """Save tasks to file"""
        try:
            tasks_data = {}

            for task_id, task in self.tasks.items():
                # Convert TaskAction objects to serializable format
                actions_data = []
                for action in task.actions:
                    action_data = {
                        'action_id': action.action_id,
                        'action_type': action.action_type.value,
                        'parameters': action.parameters,
                        'description': action.description,
                        'timeout': action.timeout,
                        'retry_count': action.retry_count
                    }
                    actions_data.append(action_data)

                task_data = {
                    'task_id': task.task_id,
                    'name': task.name,
                    'description': task.description,
                    'task_type': task.task_type.value,
                    'actions': actions_data,
                    'prerequisites': task.prerequisites,
                    'success_criteria': task.success_criteria,
                    'estimated_duration': task.estimated_duration.total_seconds(),
                    'priority': task.priority,
                    'created_at': task.created_at.isoformat(),
                    'success_rate': task.success_rate,
                    'execution_count': task.execution_count
                }

                tasks_data[task_id] = task_data

            with open('zara_automation_tasks.json', 'w') as f:
                json.dump(tasks_data, f, indent=2)

            logger.info(f"💾 Saved {len(self.tasks)} tasks")

        except Exception as e:
            logger.error(f"❌ Error saving tasks: {e}")

class TaskScheduler:
    """Intelligent task scheduler"""

    def __init__(self):
        self.is_active = False
        self.scheduled_tasks: List[Tuple[datetime, str]] = []  # (time, task_id)

    async def start_scheduling(self):
        """Start the task scheduler"""
        self.is_active = True
        asyncio.create_task(self._scheduling_loop())

    async def stop_scheduling(self):
        """Stop the task scheduler"""
        self.is_active = False

    async def _scheduling_loop(self):
        """Main scheduling loop"""
        while self.is_active:
            try:
                current_time = datetime.now()

                # Check for scheduled tasks
                due_tasks = [
                    (scheduled_time, task_id)
                    for scheduled_time, task_id in self.scheduled_tasks
                    if scheduled_time <= current_time
                ]

                # Execute due tasks
                for scheduled_time, task_id in due_tasks:
                    self.scheduled_tasks.remove((scheduled_time, task_id))
                    # Would execute task here
                    logger.info(f"⏰ Executing scheduled task: {task_id}")

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"❌ Error in scheduling loop: {e}")
                await asyncio.sleep(60)

class TaskBuilder:
    """Helper class for building automation tasks"""

    def __init__(self):
        pass

    def create_form_filling_task(self, form_data: Dict[str, str], form_name: str = "Form") -> AutomationTask:
        """Create a task for filling out forms"""
        try:
            actions = []
            action_counter = 0

            for field_name, field_value in form_data.items():
                # Click on field
                click_action = TaskAction(
                    action_id=f"click_{action_counter}",
                    action_type=ActionType.CLICK,
                    parameters={'target': {'text': field_name}},
                    description=f"Click on {field_name} field"
                )
                actions.append(click_action)
                action_counter += 1

                # Type value
                type_action = TaskAction(
                    action_id=f"type_{action_counter}",
                    action_type=ActionType.TYPE,
                    parameters={'text': field_value},
                    description=f"Enter {field_value} in {field_name}"
                )
                actions.append(type_action)
                action_counter += 1

            # Submit form
            submit_action = TaskAction(
                action_id=f"submit_{action_counter}",
                action_type=ActionType.CLICK,
                parameters={'target': {'text': 'submit'}},
                description="Submit the form"
            )
            actions.append(submit_action)

            task_id = f"form_fill_{int(time.time())}"

            return AutomationTask(
                task_id=task_id,
                name=f"Fill {form_name}",
                description=f"Automatically fill out {form_name} with provided data",
                task_type=TaskType.FORM_FILLING,
                actions=actions,
                prerequisites=[],
                success_criteria={'form_submitted': True},
                estimated_duration=timedelta(seconds=len(actions) * 3),
                priority=7,
                created_at=datetime.now()
            )

        except Exception as e:
            logger.error(f"❌ Error creating form filling task: {e}")
            return None

# Global instance
automation_system = AdvancedTaskAutomation()
