import asyncio
import time
import re
import threading
from livekit import rtc
from livekit.agents.utils import images
import asyncio
from livekit.rtc import VideoBufferType
from typing import Any, List, Optional, Dict
from dotenv import load_dotenv
from datetime import datetime
from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import noise_cancellation
from livekit.plugins import google
from livekit.agents.llm.chat_context import ChatContext
import numpy as np
import cv2
from collections import deque
import json
import logging
from concurrent.futures import ThreadPoolExecutor

# Import status writer for GUI synchronization
from zara_status_writer import (
    update_zara_status, 
    set_zara_voice_active, 
    set_zara_response, 
    set_zara_connected
)

# Import prompts and tools
from prompts import (
    AGENT_INSTRUCTION,
    SESSION_INSTRUCTION,
    AGENT_INSTRUCTION_FOR_TOOLS
)

from features import (
    enhanced_get_weather,
    enhanced_search_web,
    enhanced_play_media,
    enhanced_get_time_info,
    enhanced_system_power_action,
    enhanced_manage_window,
    enhanced_desktop_control,
    enhanced_list_active_windows,
    enhanced_manage_window_state,
    enhanced_say_reminder,
    enhanced_send_whatsapp_message,
    enhanced_write_in_notepad,
    enhanced_open_app,
    enhanced_press_key,
    enhanced_get_system_info,
    enhanced_type_user_message_auto,
    enhanced_scan_system_for_viruses,
    enhanced_click_on_text,
    enhanced_enable_camera_analysis,
    enhanced_analyze_visual_scene,
    enhanced_send_email,
    enhanced_load_and_analyze_excel,
    enhanced_get_analysis_report,
    enhanced_get_analysis_status,
    enhanced_create_visualizations_chart,
    enhanced_quick_data_analysis,
    enhanced_advanced_network_scan,
    # Enhanced Memory system functions
    enhanced_remember_user_info,
    enhanced_recall_user_info,
    enhanced_add_personal_reminder,
    enhanced_get_conversation_history,
    enhanced_get_memory_statistics,
    # Enhanced mouse and interaction functions
    enhanced_move_mouse_to_position,
    enhanced_get_mouse_position,
    # 🔥 ENHANCED REVOLUTIONARY LIVE SCREEN MONITORING 🔥
    enhanced_start_continuous_screen_monitoring,
    enhanced_stop_continuous_screen_monitoring,
    enhanced_get_current_screen_context,
    enhanced_analyze_screen_for_task,
    enhanced_screen_status_report,
    enhanced_capture_live_screen,
    # Enhanced performance tools
    enhanced_get_performance_stats,
    enhanced_optimize_system_performance,
    enhanced_cache_management,
    enhanced_function_performance_report,
    enhanced_system_status,
    # 🌟 ZARA SOUL INTEGRATION FUNCTIONS
    enhanced_awaken_zara_soul,
    enhanced_get_soul_status,
    enhanced_sleep_zara_soul,
    enhanced_get_consciousness_level
)

# Import remaining functions from tools.py that are not enhanced yet
from tools import (
    get_today_reminder_message_from_db
)

try:
    from livekit.plugins.google.beta.realtime.custom_plugins import EffectPlugin
except ImportError:
    pass

import time
import logging
from logging.handlers import RotatingFileHandler  # For log rotation


load_dotenv()

class Assistant(Agent):
    def __init__(self) -> None:
        # Initialize state firstNow 
        self._visual_analysis_enabled: bool = False
        self._last_tool_used: Optional[str] = None
        self._last_tool_success: bool = False
        self._chat_log_path = "chat_log.txt"
        
        # Initialize status writer
        update_zara_status("initializing", message="ZARA Assistant initializing...")

        # Visual Analysis Runtime State
        self._min_frame_interval: float = 0.5  # Process one frame every 0.5s (2 FPS max)
        self._max_buffer_size: int = 20        # Store a max of 20 frames (~10 seconds of context)
        self._max_frames_to_send: int = 3      # Send a maximum of 3 frames to the API
        self._frame_buffer = deque(maxlen=self._max_buffer_size) # Use deque for efficiency
        self._last_frame_time: float = 0
        self._is_analyzing: bool = False
        self._analysis_lock = asyncio.Lock()

        # ⚡ ULTRA-FAST TOOL EXECUTION SYSTEM ⚡ (Initialize BEFORE parent class)
        self._tool_executor = ThreadPoolExecutor(max_workers=8, thread_name_prefix="ZARA-ToolExec")
        self._tool_cache = {}
        self._auto_execution_enabled = True
        self._execution_timeout = 5.0  # 5 seconds max per tool

        # Performance tracking
        self._tool_execution_times = {}
        self._total_tool_calls = 0
        self._successful_tool_calls = 0

        # Memory system integration
        self._current_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self._last_user_message = ""
        self._last_assistant_response = ""
        self._tools_used_in_conversation = []

        # Initialize enhanced tools with validation
        import features
        features.assistant_instance = self
        self._tools = self._initialize_tools([
            enhanced_get_weather,
            enhanced_search_web,
            enhanced_play_media,
            enhanced_get_time_info,
            enhanced_system_power_action,
            enhanced_manage_window,
            enhanced_desktop_control,
            enhanced_list_active_windows,
            enhanced_manage_window_state,
            enhanced_send_whatsapp_message,
            enhanced_write_in_notepad,
            enhanced_open_app,
            enhanced_press_key,
            enhanced_get_system_info,
            enhanced_type_user_message_auto,
            enhanced_scan_system_for_viruses,
            enhanced_click_on_text,
            enhanced_enable_camera_analysis,
            enhanced_analyze_visual_scene,
            enhanced_send_email,
            enhanced_load_and_analyze_excel,
            enhanced_get_analysis_status,
            enhanced_get_analysis_report,
            enhanced_create_visualizations_chart,
            enhanced_quick_data_analysis,
            enhanced_advanced_network_scan,
            # Enhanced Memory system tools
            enhanced_remember_user_info,
            enhanced_recall_user_info,
            enhanced_add_personal_reminder,
            enhanced_get_conversation_history,
            enhanced_get_memory_statistics,
            # Enhanced mouse and interaction tools
            enhanced_move_mouse_to_position,
            enhanced_get_mouse_position,
            # 🔥 ENHANCED REVOLUTIONARY LIVE SCREEN MONITORING 🔥
            enhanced_start_continuous_screen_monitoring,
            enhanced_stop_continuous_screen_monitoring,
            enhanced_get_current_screen_context,
            enhanced_analyze_screen_for_task,
            enhanced_screen_status_report,
            enhanced_capture_live_screen,
            # Enhanced performance tools
            enhanced_get_performance_stats,
            enhanced_optimize_system_performance,
            enhanced_cache_management,
            enhanced_function_performance_report,
            enhanced_system_status,
            # 🌟 ZARA SOUL INTEGRATION TOOLS
            enhanced_awaken_zara_soul,
            enhanced_get_soul_status,
            enhanced_sleep_zara_soul,
            enhanced_get_consciousness_level,
            enhanced_say_reminder
        ])

        super().__init__(
            instructions=self._build_instructions(),
            llm=google.beta.realtime.RealtimeModel(
                voice="kore",
                temperature=0.5,
                top_p=0.9,

            ),
            tools=self._tools,
        )
        
        # Update status after initialization
        update_zara_status("ready", message="ZARA Assistant ready for voice commands")

        # Register effect callback if available
        try:
            EffectPlugin.register_effect_callback(self._trigger_gui_effect)
        except:
            pass

        # Live screen monitoring settings (BEFORE tool patterns)
        self._live_monitoring_enabled = True
        self._auto_start_monitoring = True

        # Initialize tool patterns AFTER tools are loaded
        self._tool_patterns = self._initialize_tool_patterns()

        # 🔥 AUTOMATICALLY START LIVE SCREEN MONITORING 🔥 (After all attributes are set)
        self._auto_start_live_monitoring()
        

    async def enable_visual_analysis(self, enable: bool):
        """Toggle visual analysis with proper frame refresh"""
        self._visual_analysis_enabled = enable
        if enable:
            self._frame_buffer.clear()
            self._last_frame_time = 0
            return "कैमरा विश्लेषण सक्रिय किया गया"
        else:
            self._frame_buffer.clear()
            if hasattr(self.llm, "session") and self.llm.session():
                self.llm.session().clear_video()
            return "कैमरा विश्लेषण निष्क्रिय किया गया"  # Camera deactivated in Hindi

    async def process_visual_frame(self, frame: rtc.VideoFrame):
        """
        Process incoming frames with throttling and efficient buffering.
        This function is called frequently by the video stream.
        """
        if not self._visual_analysis_enabled:
            return
            
        current_time = time.time()
        # Enforce a minimum interval between processed frames to reduce load
        if current_time - self._last_frame_time < self._min_frame_interval:
            return
            
        try:
            if frame.type != VideoBufferType.RGBA:
                frame = frame.convert(VideoBufferType.RGBA)
                
            # Add timestamp directly to the frame object for later retrieval
            frame.timestamp = current_time
            self._frame_buffer.append(frame) # deque handles maxlen automatically
            self._last_frame_time = current_time
            
        except Exception as e:
            # Use the agent's logger if available, otherwise print
            logger = getattr(self, 'logger', logging)
            logger.error(f"Frame processing error: {str(e)}")
            self._frame_buffer.clear()

    async def analyze_current_scene(self, prompt: str) -> str:
        """
        Analyzes a limited, representative sample of frames to avoid API quota issues.
        """
        if not self._visual_analysis_enabled:
            return "कृपया पहले कैमरा सक्रिय करें"
            
        if not self._frame_buffer:
            return "कोई ताजा फ्रेम उपलब्ध नहीं"

        async with self._analysis_lock:
            try:
                self._is_analyzing = True
                
                # --- KEY CHANGE: Smart Frame Selection ---
                # Instead of sending all recent frames, we select a small,
                # representative sample from the buffer.
                frames_to_send = []
                buffer_len = len(self._frame_buffer)
                
                if buffer_len > 0:
                    # Create a set of indices to pick: first, middle, and last.
                    # Using a set automatically handles duplicates if buffer is small.
                    indices_to_pick = {0, buffer_len // 2, buffer_len - 1}
                    
                    # Select the frames from the buffer using the chosen indices
                    selected_frames = [self._frame_buffer[i] for i in sorted(list(indices_to_pick))]
                    
                    # Ensure we don't exceed the configured max number of frames
                    frames_to_send = selected_frames[:self._max_frames_to_send]

                # Convert frames to the format expected by the LLM
                if frames_to_send and hasattr(self.llm, "session") and self.llm.session():
                    # Send frames to the LLM session for analysis
                    for frame in frames_to_send:
                        await self.llm.session().send_video_frame(frame)

                    # Request analysis with the provided prompt
                    analysis_result = await self.llm.session().analyze_video(prompt)
                    return f"दृश्य विश्लेषण: {analysis_result}"
                else:
                    return "विश्लेषण सेवा अनुपलब्ध है"

            except Exception as e:
                return f"विश्लेषण त्रुटि: {str(e)}"
            finally:
                self._is_analyzing = False

    def _initialize_tools(self, tools_list):
        """Initialize and validate tools"""
        validated_tools = []
        for tool in tools_list:
            try:
                # Validate tool has required attributes
                if hasattr(tool, '__name__') and callable(tool):
                    validated_tools.append(tool)
                    print(f"✅ Tool loaded: {tool.__name__}")
                else:
                    print(f"⚠️ Invalid tool skipped: {tool}")
            except Exception as e:
                print(f"❌ Tool loading failed: {tool} - {e}")

        print(f"🔧 Total tools loaded: {len(validated_tools)}")
        return validated_tools

    def _initialize_tool_patterns(self) -> Dict[str, Dict]:
        """Initialize ultra-fast tool pattern matching for instant execution"""
        return {
            # Weather patterns - instant execution
            'weather': {
                'patterns': [
                    r'weather\s+(?:in\s+)?(\w+)',
                    r'(\w+)\s+(?:का|की|के)\s*(?:मौसम|weather)',
                    r'(?:मौसम|weather).*?(\w+)',
                    r'temperature.*?(\w+)',
                    r'(\w+).*?(?:temperature|तापमान)'
                ],
                'tool': enhanced_get_weather,
                'cache_duration': 600,  # 10 minutes
                'priority': 1
            },

            # Time patterns - instant execution
            'time': {
                'patterns': [
                    r'(?:time|समय|टाइम)',
                    r'(?:date|तारीख|डेट)',
                    r'(?:day|दिन|वार)',
                    r'(?:clock|घड़ी)'
                ],
                'tool': enhanced_get_time_info,
                'cache_duration': 60,  # 1 minute
                'priority': 1
            },

            # Search patterns - fast execution
            'search': {
                'patterns': [
                    r'search\s+(.+)',
                    r'(?:खोज|search).*?(.+)',
                    r'(?:who is|what is|कौन है|क्या है)\s+(.+)',
                    r'(?:tell me about|बताओ)\s+(.+)',
                    r'(?:information about|जानकारी)\s+(.+)'
                ],
                'tool': enhanced_search_web,
                'cache_duration': 1800,  # 30 minutes
                'priority': 2
            },

            # System control patterns - immediate execution
            'system': {
                'patterns': [
                    r'(?:shutdown|बंद करो|शटडाउन)',
                    r'(?:restart|रीस्टार्ट|फिर से चालू)',
                    r'(?:lock|लॉक करो|स्क्रीन लॉक)'
                ],
                'tool': enhanced_system_power_action,
                'cache_duration': 0,  # No caching for system commands
                'priority': 1
            },

            # Window management - instant execution
            'window': {
                'patterns': [
                    r'(?:close|बंद करो)\s*(?:window|विंडो)?',
                    r'(?:minimize|छोटा करो|minimize करो)',
                    r'(?:maximize|बड़ा करो|maximize करो)',
                    r'(?:restore|सामान्य करो)'
                ],
                'tool': enhanced_manage_window,
                'cache_duration': 0,
                'priority': 1
            },

            # System info - fast execution
            'system_info': {
                'patterns': [
                    r'(?:system info|सिस्टम जानकारी)',
                    r'(?:specs|specifications|स्पेसिफिकेशन)',
                    r'(?:cpu|ram|memory|मेमोरी)',
                    r'(?:performance|प्रदर्शन)'
                ],
                'tool': enhanced_get_system_info,
                'cache_duration': 60,  # 1 minute
                'priority': 2
            }
        }

    async def _ultra_fast_tool_execution(self, user_input: str) -> Optional[str]:
        """
        ⚡ ULTRA-FAST TOOL EXECUTION ENGINE ⚡
        Executes tools in <0.5 seconds using pattern matching and caching
        """
        start_time = time.time()

        try:
            user_input_lower = user_input.lower().strip()

            # Check cache first for instant responses
            cache_key = f"input:{hash(user_input_lower)}"
            if cache_key in self._tool_cache:
                cached_result, cache_time, cache_duration = self._tool_cache[cache_key]
                if time.time() - cache_time < cache_duration:
                    print(f"⚡ INSTANT CACHE HIT: {user_input[:30]}... -> {cached_result[:50]}...")
                    return cached_result

            # Pattern matching for instant tool detection
            for tool_name, tool_config in self._tool_patterns.items():
                for pattern in tool_config['patterns']:
                    match = re.search(pattern, user_input_lower, re.IGNORECASE)
                    if match:
                        print(f"⚡ PATTERN MATCHED: {tool_name} -> {pattern}")

                        # Extract parameters
                        params = self._extract_tool_parameters(tool_name, match, user_input)

                        # Execute tool with timeout
                        result = await self._execute_tool_with_timeout(
                            tool_config['tool'],
                            params,
                            timeout=self._execution_timeout
                        )

                        if result:
                            # Cache successful results
                            if tool_config['cache_duration'] > 0:
                                self._tool_cache[cache_key] = (
                                    result,
                                    time.time(),
                                    tool_config['cache_duration']
                                )

                            # Track performance
                            execution_time = time.time() - start_time
                            self._track_tool_performance(tool_name, execution_time, True)

                            print(f"⚡ ULTRA-FAST EXECUTION: {tool_name} in {execution_time:.3f}s")
                            return result

            return None

        except Exception as e:
            print(f"❌ Ultra-fast execution error: {e}")
            return None

    def _extract_tool_parameters(self, tool_name: str, match, user_input: str) -> Dict:
        """Extract parameters for specific tools"""
        params = {}

        if tool_name == 'weather':
            # Extract city name
            if match.groups():
                params['city'] = match.group(1).strip()
            else:
                # Default to Delhi if no city specified
                params['city'] = 'Delhi'

        elif tool_name == 'search':
            # Extract search query
            if match.groups():
                params['query'] = match.group(1).strip()
            else:
                # Use the entire input as query
                params['query'] = user_input.strip()

        elif tool_name == 'system':
            # Extract system action
            if 'shutdown' in user_input.lower() or 'बंद करो' in user_input:
                params['action'] = 'shutdown'
            elif 'restart' in user_input.lower() or 'रीस्टार्ट' in user_input:
                params['action'] = 'restart'
            elif 'lock' in user_input.lower() or 'लॉक' in user_input:
                params['action'] = 'lock'

        elif tool_name == 'window':
            # Extract window action
            if 'close' in user_input.lower() or 'बंद करो' in user_input:
                params['action'] = 'close'
            elif 'minimize' in user_input.lower() or 'छोटा करो' in user_input:
                params['action'] = 'minimize'
            elif 'maximize' in user_input.lower() or 'बड़ा करो' in user_input:
                params['action'] = 'maximize'
            elif 'restore' in user_input.lower() or 'सामान्य करो' in user_input:
                params['action'] = 'restore'

        return params

    async def _execute_tool_with_timeout(self, tool_func, params: Dict, timeout: float = 5.0) -> Optional[str]:
        """Execute tool with timeout for guaranteed fast response"""
        try:
            # Execute tool with timeout
            if params:
                result = await asyncio.wait_for(
                    tool_func(**params),
                    timeout=timeout
                )
            else:
                result = await asyncio.wait_for(
                    tool_func(),
                    timeout=timeout
                )

            self._successful_tool_calls += 1
            return result

        except asyncio.TimeoutError:
            print(f"⏱️ Tool execution timeout: {tool_func.__name__}")
            return f"⏱️ {tool_func.__name__} execution timed out. Please try again."

        except Exception as e:
            print(f"❌ Tool execution error: {tool_func.__name__} - {e}")
            return f"❌ Error executing {tool_func.__name__}: {str(e)}"

        finally:
            self._total_tool_calls += 1

    def _track_tool_performance(self, tool_name: str, execution_time: float, success: bool):
        """Track tool performance for optimization"""
        if tool_name not in self._tool_execution_times:
            self._tool_execution_times[tool_name] = []

        self._tool_execution_times[tool_name].append({
            'time': execution_time,
            'success': success,
            'timestamp': time.time()
        })

        # Keep only last 100 executions per tool
        if len(self._tool_execution_times[tool_name]) > 100:
            self._tool_execution_times[tool_name] = self._tool_execution_times[tool_name][-100:]

    def _build_instructions(self) -> str:
        """Build comprehensive instructions for the agent"""
        return f"""
{AGENT_INSTRUCTION}

{AGENT_INSTRUCTION_FOR_TOOLS}

## ⚡ ULTRA-FAST EXECUTION MODE ACTIVE ⚡
ZARA 3.0 is equipped with lightning-fast tool execution capabilities:
- Pattern-based instant tool detection (<0.1s)
- Smart caching for repeated requests (10min-30min TTL)
- Parallel execution for maximum speed
- Sub-second response times for common operations
- Automatic tool selection and parameter extraction

## 🔥 REVOLUTIONARY LIVE SCREEN MONITORING ACTIVE 🔥
ZARA 3.0 now continuously watches your screen with advanced capabilities:
- 📺 Real-time monitoring of all input boxes and text fields
- 🎯 Automatic detection of buttons, dropdowns, and UI elements
- 📝 Live text recognition and content analysis
- 🔍 Focus tracking and active element detection
- 👁️ Continuous screen analysis with change detection
- ⚡ Instant response to screen interactions and changes

## CURRENT SESSION CONTEXT
- Assistant Name: ZARA 3.0 (Enhanced Performance Edition)
- Creator: Ratnam Sanjay
- Session Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Available Tools: {len(self._tools)} functions
- Visual Analysis: {'Enabled' if self._visual_analysis_enabled else 'Disabled'}
- Ultra-Fast Execution: {'Enabled' if self._auto_execution_enabled else 'Disabled'}
- Tool Cache Entries: {len(self._tool_cache)}
- Total Tool Calls: {self._total_tool_calls}
- Success Rate: {(self._successful_tool_calls/self._total_tool_calls*100) if self._total_tool_calls > 0 else 100:.1f}%

## ENHANCED BEHAVIORAL GUIDELINES
1. ⚡ SPEED FIRST: Execute tools instantly using pattern matching
2. 🎯 ACCURACY: Use cached results for repeated requests
3. 🗣️ NATURAL: Respond in Hindi/English based on user preference
4. 🤝 HELPFUL: Maintain respectful and professional male AI tone
5. ✅ CONFIRM: Acknowledge successful tool execution briefly
6. 🚀 PROACTIVE: Offer follow-up assistance and optimizations

## INSTANT EXECUTION PRIORITIES
1. Weather queries → enhanced_get_weather() [10min cache]
2. Time/Date requests → enhanced_get_time_info() [1min cache]
3. System commands → enhanced_system_power_action() [no cache]
4. Window management → manage_window() [no cache]
5. Search queries → search_web() [30min cache]
6. System info → get_system_info() [1min cache]

{SESSION_INSTRUCTION}
"""

    async def _trigger_gui_effect(self, effect_data: dict):
        """Handle GUI effects and visual feedback"""
        try:
            effect_type = effect_data.get("type", "unknown")
            print(f"🎨 GUI Effect triggered: {effect_type}")

            # Handle different effect types
            if effect_type == "tool_execution":
                self._last_tool_used = effect_data.get("tool_name")
                self._last_tool_success = effect_data.get("success", False)
            elif effect_type == "visual_analysis":
                # Handle visual analysis effects
                pass
            elif effect_type == "system_notification":
                # Handle system notifications
                pass

        except Exception as e:
            print(f"❌ GUI Effect error: {e}")

    async def generate_startup_message(self, session):
        """
        Dynamically constructs startup instructions by combining the base SESSION_INSTRUCTION
        with a summary of the last conversation, then generates a context-aware greeting.
        """
        try:
            # It's good practice to import DB functions inside the method that uses them.
            # get_today_reminder_message_from_db is already imported at the top
            import json # Ensure json is imported

            # Start with the base instructions you've already written.
            final_instructions = SESSION_INSTRUCTION

            # Check for reminders instead of chat history for now
            reminder_text = await get_today_reminder_message_from_db()

            if reminder_text:
                print("✅ Found reminders. Adding reminder context to startup instructions.")

                # Append the reminder instructions to the existing ones.
                final_instructions += (
                    "\n\n"
                    "🔰 विशेष संदर्भ:\n"
                    "आज के लिए कुछ याददाश्त हैं। कृपया अपने अभिवादन में इसका उल्लेख करें।\n"
                    f"आज की याददाश्त: {reminder_text}\n"
                    "--------------------------"
                )
            else:
                # If there's no reminders, we don't need to add anything.
                # The base SESSION_INSTRUCTION is sufficient for a first-time greeting.
                print("✅ No reminders found. Using standard startup instructions.")

            # Use the final, combined instructions to generate the spoken reply.
            await session.generate_reply(instructions=final_instructions)

        except Exception as e:
            print(f"⚠️ Failed to generate startup message: {e}")
            # Fallback to a simple greeting if there's an error.
            await session.generate_reply(instructions="नमस्कार sanjay sir, मैं ZARA आपकी सेवा में प्रस्तुत हूँ।")

    async def _store_conversation_in_memory(self, user_message: str, assistant_response: str, tools_used: List[str] = None):
        """Store conversation in memory system"""
        try:
            # Import memory functions
            from tools import store_conversation, MEMORY_AVAILABLE

            if MEMORY_AVAILABLE and user_message and assistant_response:
                await store_conversation(
                    session_id=self._current_session_id,
                    user_msg=user_message,
                    assistant_msg=assistant_response,
                    tools=tools_used or []
                )
                print(f"✅ Conversation stored in memory")

        except Exception as e:
            print(f"⚠️ Failed to store conversation in memory: {e}")

    async def _on_user_speech_committed(self, session, message):
        """Handle user speech with ultra-fast tool execution"""
        try:
            self._last_user_message = message.content
            self._tools_used_in_conversation = []
            print(f"👤 User said: {message.content}")

            # Update status for GUI
            update_zara_status("processing", message=f"Processing: {message.content[:50]}...")
            set_zara_voice_active(False)  # User finished speaking

            # ⚡ ULTRA-FAST TOOL EXECUTION ⚡
            if self._auto_execution_enabled:
                fast_result = await self._ultra_fast_tool_execution(message.content)
                if fast_result:
                    print(f"⚡ INSTANT TOOL RESULT: {fast_result[:100]}...")

                    # Send immediate response
                    await session.generate_reply(
                        instructions=f"""
                        User asked: "{message.content}"

                        Tool executed instantly with result: {fast_result}

                        Provide a brief, natural response in Hindi/English acknowledging the result.
                        Be conversational and helpful. Don't repeat the entire result, just acknowledge it naturally.
                        """
                    )

                    # Update status
                    update_zara_status("completed", message="Tool executed successfully")
                    return

        except Exception as e:
            print(f"⚠️ Error handling user speech: {e}")
            update_zara_status("error", message=f"Error handling speech: {e}")

    async def _on_assistant_speech_committed(self, session, message):
        """Handle assistant response and store conversation"""
        try:
            self._last_assistant_response = message.content
            print(f"🤖 ZARA responded: {message.content}")

            # Update status for GUI
            update_zara_status("speaking", message="Responding to user")
            set_zara_response(message.content)
            set_zara_voice_active(True)  # Assistant is speaking

            # Store the complete conversation in memory
            await self._store_conversation_in_memory(
                user_message=self._last_user_message,
                assistant_response=self._last_assistant_response,
                tools_used=self._tools_used_in_conversation
            )
            
            # Return to listening after speaking
            import asyncio
            await asyncio.sleep(1)  # Brief delay
            update_zara_status("listening", message="Ready for next command")
            set_zara_voice_active(False)

        except Exception as e:
            print(f"⚠️ Error handling assistant speech: {e}")
            update_zara_status("error", message=f"Error in response: {e}")

    async def _on_function_calls_finished(self, session, called_functions):
        """Track tools used in conversation with performance monitoring"""
        try:
            for func_call in called_functions:
                tool_name = func_call.function_info.name
                self._tools_used_in_conversation.append(tool_name)

                # Track execution time if available
                execution_time = getattr(func_call, 'execution_time', 0)
                if execution_time > 0:
                    self._track_tool_performance(tool_name, execution_time, True)
                    print(f"⚡ Tool executed: {tool_name} in {execution_time:.3f}s")
                else:
                    print(f"🔧 Tool used: {tool_name}")

                # Update status for each tool execution
                update_zara_status("processing",
                                 message=f"Executing tool: {tool_name}",
                                 current_tool=tool_name)

        except Exception as e:
            print(f"⚠️ Error tracking function calls: {e}")
            update_zara_status("error", message=f"Error in tool execution: {e}")

    async def get_performance_report(self) -> str:
        """Get comprehensive performance report for ZARA"""
        try:
            total_calls = self._total_tool_calls
            successful_calls = self._successful_tool_calls
            success_rate = (successful_calls / total_calls * 100) if total_calls > 0 else 0

            report = f"""
⚡ **ZARA 3.0 PERFORMANCE REPORT** ⚡

📊 **Overall Statistics:**
• Total Tool Calls: {total_calls}
• Successful Calls: {successful_calls}
• Success Rate: {success_rate:.1f}%
• Cache Entries: {len(self._tool_cache)}

🚀 **Tool Performance:**
"""

            # Add individual tool performance
            for tool_name, executions in self._tool_execution_times.items():
                if executions:
                    avg_time = sum(e['time'] for e in executions) / len(executions)
                    min_time = min(e['time'] for e in executions)
                    max_time = max(e['time'] for e in executions)

                    if avg_time < 0.5:
                        rating = "⚡ Ultra-Fast"
                    elif avg_time < 1.0:
                        rating = "🚀 Fast"
                    elif avg_time < 2.0:
                        rating = "✅ Normal"
                    else:
                        rating = "⚠️ Slow"

                    report += f"• {tool_name}: {avg_time:.3f}s avg ({min_time:.3f}-{max_time:.3f}s) {rating}\n"

            report += f"\n🕐 Report Generated: {datetime.now().strftime('%I:%M %p')}"

            return report

        except Exception as e:
            return f"❌ Error generating performance report: {e}"

    def _auto_start_live_monitoring(self):
        """🔥 Automatically start live screen monitoring on ZARA startup"""
        try:
            if self._auto_start_monitoring and self._live_monitoring_enabled:
                # Start monitoring in a separate thread to avoid blocking startup
                def start_monitoring():
                    try:
                        # Use enhanced monitoring function
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        result = loop.run_until_complete(enhanced_start_continuous_screen_monitoring())
                        print(f"🔥 ENHANCED AUTO-STARTED: {result}")

                        # Update status
                        update_zara_status("monitoring",
                                         message="Live screen monitoring active - watching all input boxes and UI elements")

                    except Exception as e:
                        print(f"❌ Auto-start live monitoring failed: {e}")

                # Start in background thread
                monitor_thread = threading.Thread(target=start_monitoring, daemon=True)
                monitor_thread.start()

                print("🔥 ZARA 3.0 - Live Screen Monitoring will start automatically...")

        except Exception as e:
            print(f"❌ Failed to auto-start live monitoring: {e}")

    async def get_live_screen_info(self) -> str:
        """Get current enhanced live screen monitoring information"""
        try:
            # Use enhanced screen status function
            result = await enhanced_screen_status_report()
            return result

        except Exception as e:
            return f"❌ Error getting enhanced live screen info: {e}"

    async def _check_reminders(self) -> None:
        """Check and announce any reminders."""
        try:
            # Fix: Add await keyword here since get_today_reminder_message_from_db is async
            reminder_text = await get_today_reminder_message_from_db()
            if reminder_text:
                await enhanced_say_reminder(reminder_text)
        except Exception as e:
            print(f"⚠️ Failed to check reminders: {str(e)}")



import os

async def entrypoint(ctx: agents.JobContext):
    """Main entrypoint for ZARA voice assistant with enhanced error handling and retry logic"""

    max_retries = 3
    retry_delay = 2.0

    print("🚀 ZARA 2.0 Voice Assistant Starting...")
    print(f"📍 Room: {ctx.room.name}")
    print(f"🔗 LiveKit URL: {os.getenv('LIVEKIT_URL', 'Not configured')}")
    
    # Update status
    update_zara_status("connecting", message="Connecting to LiveKit room...")
    set_zara_connected(False)

    for attempt in range(1, max_retries + 1):
        try:
            # Create agent first
            agent = Assistant()

            # Create session with the agent
            session = AgentSession()

            # Start session with proper configuration
            try:
                await asyncio.wait_for(
                    session.start(
                        room=ctx.room,
                        agent=agent,
                        room_input_options=RoomInputOptions(
                            video_enabled=True,
                            noise_cancellation=noise_cancellation.BVC(),
                        ),
                    ),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                raise Exception("Connection timeout")

            print("🔗 Connected to LiveKit room successfully")
            
            # Update connection status
            set_zara_connected(True)
            update_zara_status("connected", message="Connected to LiveKit room")

            # Connect to the room
            await ctx.connect()

            # ==================================================
            # START: MODIFIED BLOCK
            # ==================================================

            # Instead of sending a generic instruction, call our new method.
            # This will handle the startup message generation.
            update_zara_status("starting", message="Generating startup message...")
            await agent.generate_startup_message(session)

            # ==================================================
            # END: MODIFIED BLOCK
            # ==================================================

            # Check for reminders (this can stay here)
            await agent._check_reminders()

            print("✅ Agent session started successfully and is now live.")
            update_zara_status("listening", message="ZARA is ready and listening for commands")
            break

        except Exception as e:
            print(f"❌ Entrypoint failed on attempt {attempt}: {e}")
            update_zara_status("error", message=f"Connection failed (attempt {attempt}): {e}")
            set_zara_connected(False)
            
            if attempt < max_retries:
                update_zara_status("retrying", message=f"Retrying connection in {retry_delay}s...")
                await asyncio.sleep(retry_delay)
            else:
                update_zara_status("failed", message="All connection attempts failed")
                raise  # Re-raise if all retries failed


if __name__ == "__main__":
    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))
