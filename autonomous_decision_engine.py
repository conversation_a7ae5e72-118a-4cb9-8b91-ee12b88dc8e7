#!/usr/bin/env python3
"""
ZARA Autonomous Decision Engine
Created by: <PERSON><PERSON>jay

This module provides <PERSON><PERSON> with human-like autonomous decision making:
- Context-aware reasoning and planning
- Goal-oriented task execution
- Learning from user patterns and feedback
- Proactive assistance and suggestions
- Ethical decision boundaries
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from collections import deque, defaultdict
import threading

# Import our visual perception system
from advanced_visual_perception import visual_perception, VisualScene, VisualElement, VisualContext

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DecisionType(Enum):
    """Types of decisions <PERSON><PERSON> can make"""
    REACTIVE = "reactive"  # Response to immediate user input
    PROACTIVE = "proactive"  # Anticipatory actions
    CORRECTIVE = "corrective"  # Fixing errors or problems
    EXPLORATORY = "exploratory"  # Learning new patterns
    PREVENTIVE = "preventive"  # Avoiding potential issues

class ConfidenceLevel(Enum):
    """Confidence levels for decisions"""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95

class ActionPriority(Enum):
    """Priority levels for actions"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    BACKGROUND = "background"

@dataclass
class DecisionContext:
    """Context information for making decisions"""
    current_scene: Optional[VisualScene]
    user_intent: Optional[str]
    recent_actions: List[str]
    system_state: Dict[str, Any]
    time_constraints: Optional[timedelta]
    user_preferences: Dict[str, Any]
    safety_constraints: List[str]

@dataclass
class Decision:
    """Represents a decision made by Zara"""
    decision_id: str
    decision_type: DecisionType
    action: str
    reasoning: str
    confidence: float
    priority: ActionPriority
    expected_outcome: str
    risk_assessment: Dict[str, float]
    prerequisites: List[str]
    timestamp: datetime
    context: DecisionContext
    
    def should_execute(self) -> bool:
        """Determine if this decision should be executed"""
        return (self.confidence >= ConfidenceLevel.MEDIUM.value and 
                all(risk <= 0.3 for risk in self.risk_assessment.values()))

@dataclass
class LearningPattern:
    """Pattern learned from user behavior"""
    pattern_id: str
    trigger_conditions: Dict[str, Any]
    typical_actions: List[str]
    success_rate: float
    frequency: int
    last_seen: datetime
    confidence: float

class AutonomousDecisionEngine:
    """Advanced autonomous decision making system for Zara"""
    
    def __init__(self):
        self.is_active = False
        self.decision_history: deque = deque(maxlen=1000)
        self.learning_patterns: Dict[str, LearningPattern] = {}
        self.user_preferences: Dict[str, Any] = {}
        self.safety_rules: List[str] = []
        
        # Decision making parameters
        self.min_confidence_threshold = 0.6
        self.max_decisions_per_minute = 10
        self.learning_rate = 0.1
        
        # Context tracking
        self.current_context: Optional[DecisionContext] = None
        self.context_history: deque = deque(maxlen=50)
        
        # Goal and task tracking
        self.current_goals: List[str] = []
        self.active_tasks: Dict[str, Any] = {}
        
        # Initialize safety constraints
        self._initialize_safety_rules()
        
        # Load user preferences and patterns
        self._load_user_data()
        
        # Decision execution queue
        self.decision_queue: asyncio.Queue = asyncio.Queue()
        self.execution_lock = threading.Lock()
        
    def _initialize_safety_rules(self):
        """Initialize safety rules and ethical boundaries"""
        self.safety_rules = [
            "Never delete important files without explicit confirmation",
            "Never share personal information externally",
            "Never perform actions that could harm the system",
            "Always respect user privacy and data",
            "Never execute commands that could compromise security",
            "Always ask before making significant system changes",
            "Never interfere with critical system processes",
            "Respect user's work and don't interrupt important tasks"
        ]
    
    def _load_user_data(self):
        """Load user preferences and learned patterns"""
        try:
            # Load from file if exists
            try:
                with open('zara_user_patterns.json', 'r') as f:
                    data = json.load(f)
                    self.user_preferences = data.get('preferences', {})
                    
                    # Convert pattern data back to LearningPattern objects
                    patterns_data = data.get('patterns', {})
                    for pattern_id, pattern_data in patterns_data.items():
                        self.learning_patterns[pattern_id] = LearningPattern(
                            pattern_id=pattern_data['pattern_id'],
                            trigger_conditions=pattern_data['trigger_conditions'],
                            typical_actions=pattern_data['typical_actions'],
                            success_rate=pattern_data['success_rate'],
                            frequency=pattern_data['frequency'],
                            last_seen=datetime.fromisoformat(pattern_data['last_seen']),
                            confidence=pattern_data['confidence']
                        )
                        
            except FileNotFoundError:
                # Initialize with defaults
                self.user_preferences = {
                    'automation_level': 'medium',  # low, medium, high
                    'proactive_suggestions': True,
                    'learning_enabled': True,
                    'safety_level': 'high',
                    'preferred_interaction_style': 'helpful'
                }
                
        except Exception as e:
            logger.error(f"❌ Error loading user data: {e}")
    
    async def start_decision_engine(self) -> str:
        """Start the autonomous decision engine"""
        try:
            if self.is_active:
                return "🧠 Autonomous Decision Engine already active!"
            
            self.is_active = True
            
            # Start decision processing loop
            asyncio.create_task(self._decision_processing_loop())
            
            # Start context monitoring
            asyncio.create_task(self._context_monitoring_loop())
            
            result = (
                f"🧠 **AUTONOMOUS DECISION ENGINE ACTIVATED!**\n"
                f"🎯 Intelligent Reasoning: ONLINE\n"
                f"🔮 Proactive Assistance: ENABLED\n"
                f"📚 Learning System: ACTIVE\n"
                f"🛡️ Safety Constraints: {len(self.safety_rules)} rules\n"
                f"🧩 Learned Patterns: {len(self.learning_patterns)}\n"
                f"🕐 Started: {datetime.now().strftime('%I:%M:%S %p')}"
            )
            
            logger.info("🧠 Autonomous Decision Engine Started")
            return result
            
        except Exception as e:
            self.is_active = False
            logger.error(f"❌ Failed to start decision engine: {e}")
            return f"❌ Failed to start decision engine: {e}"
    
    async def stop_decision_engine(self) -> str:
        """Stop the autonomous decision engine"""
        try:
            self.is_active = False
            
            # Save learned patterns
            await self._save_user_data()
            
            result = (
                f"🛑 **DECISION ENGINE STOPPED**\n"
                f"🧠 Autonomous Reasoning: DISABLED\n"
                f"💾 Learning Data: SAVED\n"
                f"🕐 Stopped: {datetime.now().strftime('%I:%M:%S %p')}"
            )
            
            logger.info("🛑 Autonomous Decision Engine Stopped")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error stopping decision engine: {e}")
            return f"❌ Error stopping decision engine: {e}"
    
    async def _decision_processing_loop(self):
        """Main decision processing loop"""
        while self.is_active:
            try:
                # Update current context
                await self._update_context()
                
                # Generate potential decisions
                decisions = await self._generate_decisions()
                
                # Evaluate and prioritize decisions
                for decision in decisions:
                    if decision.should_execute():
                        await self.decision_queue.put(decision)
                
                # Execute decisions from queue
                await self._execute_pending_decisions()
                
                # Learn from recent actions
                await self._learn_from_recent_actions()
                
                # Sleep before next iteration
                await asyncio.sleep(2.0)  # Process every 2 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in decision processing loop: {e}")
                await asyncio.sleep(5.0)
    
    async def _context_monitoring_loop(self):
        """Monitor context changes and update understanding"""
        while self.is_active:
            try:
                # Get current visual scene
                current_scene = visual_perception.current_scene
                
                if current_scene and self._context_changed(current_scene):
                    # Context has changed, update our understanding
                    await self._analyze_context_change(current_scene)
                
                await asyncio.sleep(1.0)  # Monitor every second
                
            except Exception as e:
                logger.error(f"❌ Error in context monitoring: {e}")
                await asyncio.sleep(3.0)
    
    async def _update_context(self):
        """Update current decision context"""
        try:
            current_scene = visual_perception.current_scene
            
            # Get recent actions from decision history
            recent_actions = [d.action for d in list(self.decision_history)[-10:]]
            
            # Get system state (simplified)
            system_state = {
                'timestamp': datetime.now(),
                'active_windows': len(current_scene.elements) if current_scene else 0,
                'visual_context': current_scene.context.value if current_scene else 'unknown'
            }
            
            self.current_context = DecisionContext(
                current_scene=current_scene,
                user_intent=None,  # Will be inferred
                recent_actions=recent_actions,
                system_state=system_state,
                time_constraints=None,
                user_preferences=self.user_preferences,
                safety_constraints=self.safety_rules
            )
            
        except Exception as e:
            logger.error(f"❌ Error updating context: {e}")
    
    async def _generate_decisions(self) -> List[Decision]:
        """Generate potential decisions based on current context"""
        try:
            decisions = []
            
            if not self.current_context or not self.current_context.current_scene:
                return decisions
            
            scene = self.current_context.current_scene
            
            # Generate proactive suggestions based on scene analysis
            proactive_decisions = await self._generate_proactive_decisions(scene)
            decisions.extend(proactive_decisions)
            
            # Generate corrective decisions if issues detected
            corrective_decisions = await self._generate_corrective_decisions(scene)
            decisions.extend(corrective_decisions)
            
            # Generate learning-based decisions
            learning_decisions = await self._generate_learning_decisions(scene)
            decisions.extend(learning_decisions)
            
            return decisions
            
        except Exception as e:
            logger.error(f"❌ Error generating decisions: {e}")
            return []

    async def _generate_proactive_decisions(self, scene: VisualScene) -> List[Decision]:
        """Generate proactive decisions based on scene analysis"""
        try:
            decisions = []

            # Check for form completion opportunities
            input_fields = scene.get_elements_by_type(VisualElementType.INPUT_FIELD)
            if len(input_fields) > 2:
                decision = Decision(
                    decision_id=f"proactive_form_{int(time.time())}",
                    decision_type=DecisionType.PROACTIVE,
                    action="offer_form_assistance",
                    reasoning="Multiple input fields detected, user might need form filling assistance",
                    confidence=0.7,
                    priority=ActionPriority.LOW,
                    expected_outcome="Faster form completion",
                    risk_assessment={'privacy': 0.1, 'accuracy': 0.2},
                    prerequisites=["user_consent"],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            # Check for repetitive task patterns
            if self._detect_repetitive_pattern():
                decision = Decision(
                    decision_id=f"automation_{int(time.time())}",
                    decision_type=DecisionType.PROACTIVE,
                    action="suggest_automation",
                    reasoning="Repetitive task pattern detected",
                    confidence=0.8,
                    priority=ActionPriority.MEDIUM,
                    expected_outcome="Reduced manual work",
                    risk_assessment={'error': 0.15},
                    prerequisites=["pattern_confirmation"],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            # Check for optimization opportunities
            if scene.context == VisualContext.BROWSER and len(scene.elements) > 20:
                decision = Decision(
                    decision_id=f"optimize_{int(time.time())}",
                    decision_type=DecisionType.PROACTIVE,
                    action="suggest_tab_management",
                    reasoning="Many elements detected in browser, might benefit from tab organization",
                    confidence=0.6,
                    priority=ActionPriority.LOW,
                    expected_outcome="Better browser performance",
                    risk_assessment={'disruption': 0.1},
                    prerequisites=[],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            return decisions

        except Exception as e:
            logger.error(f"❌ Error generating proactive decisions: {e}")
            return []

    async def _generate_corrective_decisions(self, scene: VisualScene) -> List[Decision]:
        """Generate corrective decisions for detected issues"""
        try:
            decisions = []

            # Check for error dialogs or notifications
            error_indicators = ['error', 'failed', 'problem', 'issue', 'warning']
            scene_text = scene.text_content.lower()

            if any(indicator in scene_text for indicator in error_indicators):
                decision = Decision(
                    decision_id=f"error_help_{int(time.time())}",
                    decision_type=DecisionType.CORRECTIVE,
                    action="offer_error_assistance",
                    reasoning="Error or warning detected in interface",
                    confidence=0.85,
                    priority=ActionPriority.HIGH,
                    expected_outcome="Problem resolution",
                    risk_assessment={'misinterpretation': 0.2},
                    prerequisites=["error_analysis"],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            # Check for incomplete forms
            empty_required_fields = self._detect_empty_required_fields(scene)
            if empty_required_fields:
                decision = Decision(
                    decision_id=f"form_completion_{int(time.time())}",
                    decision_type=DecisionType.CORRECTIVE,
                    action="highlight_required_fields",
                    reasoning=f"Found {len(empty_required_fields)} empty required fields",
                    confidence=0.9,
                    priority=ActionPriority.MEDIUM,
                    expected_outcome="Complete form submission",
                    risk_assessment={'false_positive': 0.1},
                    prerequisites=[],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            # Check for accessibility issues
            if self._detect_accessibility_issues(scene):
                decision = Decision(
                    decision_id=f"accessibility_{int(time.time())}",
                    decision_type=DecisionType.CORRECTIVE,
                    action="suggest_accessibility_improvements",
                    reasoning="Potential accessibility issues detected",
                    confidence=0.7,
                    priority=ActionPriority.LOW,
                    expected_outcome="Better user experience",
                    risk_assessment={'over_suggestion': 0.2},
                    prerequisites=["accessibility_analysis"],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            return decisions

        except Exception as e:
            logger.error(f"❌ Error generating corrective decisions: {e}")
            return []

    async def _generate_learning_decisions(self, scene: VisualScene) -> List[Decision]:
        """Generate decisions based on learned patterns"""
        try:
            decisions = []

            # Check learned patterns for current context
            context_key = f"{scene.context.value}_{len(scene.elements)}"

            for pattern_id, pattern in self.learning_patterns.items():
                if self._pattern_matches_context(pattern, scene):
                    # Suggest action based on learned pattern
                    confidence = pattern.confidence * pattern.success_rate

                    if confidence > self.min_confidence_threshold:
                        decision = Decision(
                            decision_id=f"learned_{pattern_id}_{int(time.time())}",
                            decision_type=DecisionType.PROACTIVE,
                            action=f"suggest_learned_action_{pattern.typical_actions[0] if pattern.typical_actions else 'unknown'}",
                            reasoning=f"Based on learned pattern: {pattern_id}",
                            confidence=confidence,
                            priority=ActionPriority.MEDIUM,
                            expected_outcome="Consistent with user preferences",
                            risk_assessment={'pattern_drift': 0.1, 'context_mismatch': 0.15},
                            prerequisites=["pattern_validation"],
                            timestamp=datetime.now(),
                            context=self.current_context
                        )
                        decisions.append(decision)

            return decisions

        except Exception as e:
            logger.error(f"❌ Error generating learning decisions: {e}")
            return []

    def _detect_repetitive_pattern(self) -> bool:
        """Detect if user is performing repetitive actions"""
        try:
            if len(self.decision_history) < 5:
                return False

            recent_actions = [d.action for d in list(self.decision_history)[-5:]]

            # Check for repeated actions
            action_counts = defaultdict(int)
            for action in recent_actions:
                action_counts[action] += 1

            # If any action appears more than 3 times in last 5 actions
            return any(count >= 3 for count in action_counts.values())

        except Exception as e:
            return False

    def _detect_empty_required_fields(self, scene: VisualScene) -> List[VisualElement]:
        """Detect empty required form fields"""
        try:
            empty_required = []

            for element in scene.elements:
                if (element.element_type == VisualElementType.INPUT_FIELD and
                    element.text and
                    ('required' in element.text.lower() or '*' in element.text)):

                    # Check if field appears empty (common indicators)
                    if (not element.text.strip() or
                        'placeholder' in element.text.lower() or
                        'enter' in element.text.lower()):
                        empty_required.append(element)

            return empty_required

        except Exception as e:
            return []

    def _detect_accessibility_issues(self, scene: VisualScene) -> bool:
        """Detect potential accessibility issues"""
        try:
            # Check for very small text
            small_text_count = 0
            for element in scene.elements:
                if (element.element_type == VisualElementType.TEXT and
                    element.bounds[3] < 12):  # Height less than 12 pixels
                    small_text_count += 1

            # Check for low contrast (simplified)
            if len(scene.dominant_colors) >= 2:
                color1, color2 = scene.dominant_colors[0], scene.dominant_colors[1]
                contrast_ratio = self._calculate_contrast_ratio(color1, color2)
                if contrast_ratio < 3.0:  # WCAG minimum
                    return True

            # Too many small text elements might indicate accessibility issues
            return small_text_count > 10

        except Exception as e:
            return False

    def _calculate_contrast_ratio(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """Calculate contrast ratio between two colors"""
        try:
            def luminance(color):
                r, g, b = [c / 255.0 for c in color]
                return 0.299 * r + 0.587 * g + 0.114 * b

            lum1 = luminance(color1)
            lum2 = luminance(color2)

            lighter = max(lum1, lum2)
            darker = min(lum1, lum2)

            return (lighter + 0.05) / (darker + 0.05)

        except Exception as e:
            return 1.0

    def _pattern_matches_context(self, pattern: LearningPattern, scene: VisualScene) -> bool:
        """Check if a learned pattern matches current context"""
        try:
            conditions = pattern.trigger_conditions

            # Check context match
            if 'context' in conditions:
                if conditions['context'] != scene.context.value:
                    return False

            # Check element count similarity
            if 'element_count' in conditions:
                expected_count = conditions['element_count']
                actual_count = len(scene.elements)
                if abs(actual_count - expected_count) > expected_count * 0.3:  # 30% tolerance
                    return False

            # Check for specific element types
            if 'required_elements' in conditions:
                scene_element_types = {e.element_type.value for e in scene.elements}
                required_elements = set(conditions['required_elements'])
                if not required_elements.issubset(scene_element_types):
                    return False

            return True

        except Exception as e:
            return False

    async def _execute_pending_decisions(self):
        """Execute decisions from the queue"""
        try:
            executed_count = 0
            max_executions = 3  # Limit executions per cycle

            while not self.decision_queue.empty() and executed_count < max_executions:
                try:
                    decision = await asyncio.wait_for(self.decision_queue.get(), timeout=0.1)

                    # Double-check if decision should still be executed
                    if decision.should_execute():
                        success = await self._execute_decision(decision)

                        # Record decision in history
                        self.decision_history.append(decision)

                        # Learn from execution result
                        await self._learn_from_execution(decision, success)

                        executed_count += 1

                except asyncio.TimeoutError:
                    break
                except Exception as e:
                    logger.error(f"❌ Error executing decision: {e}")

        except Exception as e:
            logger.error(f"❌ Error in decision execution: {e}")

    async def _execute_decision(self, decision: Decision) -> bool:
        """Execute a specific decision"""
        try:
            action = decision.action

            # Route to appropriate execution method based on action type
            if action == "offer_form_assistance":
                return await self._offer_form_assistance(decision)
            elif action == "suggest_automation":
                return await self._suggest_automation(decision)
            elif action == "suggest_tab_management":
                return await self._suggest_tab_management(decision)
            elif action == "offer_error_assistance":
                return await self._offer_error_assistance(decision)
            elif action == "highlight_required_fields":
                return await self._highlight_required_fields(decision)
            elif action.startswith("suggest_learned_action"):
                return await self._execute_learned_action(decision)
            else:
                logger.warning(f"⚠️ Unknown action type: {action}")
                return False

        except Exception as e:
            logger.error(f"❌ Error executing decision {decision.decision_id}: {e}")
            return False

    async def _offer_form_assistance(self, decision: Decision) -> bool:
        """Offer assistance with form filling"""
        try:
            # This would integrate with Zara's voice/chat interface
            message = (
                f"🤖 I notice you have a form with multiple fields. "
                f"Would you like me to help you fill it out based on your saved information?"
            )

            # Log the suggestion (in real implementation, this would be spoken/displayed)
            logger.info(f"💡 Proactive Suggestion: {message}")

            # For now, just log the decision
            return True

        except Exception as e:
            logger.error(f"❌ Error offering form assistance: {e}")
            return False

    async def _suggest_automation(self, decision: Decision) -> bool:
        """Suggest automation for repetitive tasks"""
        try:
            message = (
                f"🔄 I've noticed you're repeating similar actions. "
                f"Would you like me to automate this task for you?"
            )

            logger.info(f"💡 Automation Suggestion: {message}")
            return True

        except Exception as e:
            return False

    async def _suggest_tab_management(self, decision: Decision) -> bool:
        """Suggest browser tab management"""
        try:
            message = (
                f"🌐 Your browser seems to have many elements loaded. "
                f"Would you like me to help organize your tabs or close unused ones?"
            )

            logger.info(f"💡 Tab Management: {message}")
            return True

        except Exception as e:
            return False

    async def _offer_error_assistance(self, decision: Decision) -> bool:
        """Offer assistance with detected errors"""
        try:
            message = (
                f"⚠️ I detected an error or warning on your screen. "
                f"Would you like me to help troubleshoot this issue?"
            )

            logger.info(f"💡 Error Assistance: {message}")
            return True

        except Exception as e:
            return False

    async def _highlight_required_fields(self, decision: Decision) -> bool:
        """Highlight required form fields"""
        try:
            message = (
                f"📝 I found some required fields that appear to be empty. "
                f"Would you like me to highlight them for you?"
            )

            logger.info(f"💡 Form Validation: {message}")
            return True

        except Exception as e:
            return False

    async def _execute_learned_action(self, decision: Decision) -> bool:
        """Execute an action based on learned patterns"""
        try:
            message = (
                f"🧠 Based on your previous behavior, I think you might want to "
                f"perform a similar action. Should I proceed?"
            )

            logger.info(f"💡 Learned Pattern: {message}")
            return True

        except Exception as e:
            return False

    async def _learn_from_execution(self, decision: Decision, success: bool):
        """Learn from the execution result"""
        try:
            # Update pattern success rates
            if decision.action.startswith("suggest_learned_action"):
                pattern_id = decision.reasoning.split(": ")[-1]
                if pattern_id in self.learning_patterns:
                    pattern = self.learning_patterns[pattern_id]

                    # Update success rate using exponential moving average
                    alpha = self.learning_rate
                    new_success = 1.0 if success else 0.0
                    pattern.success_rate = (1 - alpha) * pattern.success_rate + alpha * new_success
                    pattern.frequency += 1
                    pattern.last_seen = datetime.now()

            # Learn new patterns from successful decisions
            if success and decision.confidence > 0.8:
                await self._create_learning_pattern(decision)

        except Exception as e:
            logger.error(f"❌ Error learning from execution: {e}")

    async def _create_learning_pattern(self, decision: Decision):
        """Create a new learning pattern from successful decision"""
        try:
            if not decision.context.current_scene:
                return

            scene = decision.context.current_scene
            pattern_id = f"pattern_{scene.context.value}_{decision.action}_{int(time.time())}"

            # Create trigger conditions based on current context
            trigger_conditions = {
                'context': scene.context.value,
                'element_count': len(scene.elements),
                'required_elements': [e.element_type.value for e in scene.elements[:5]]  # Top 5 elements
            }

            pattern = LearningPattern(
                pattern_id=pattern_id,
                trigger_conditions=trigger_conditions,
                typical_actions=[decision.action],
                success_rate=1.0,  # Start with perfect success
                frequency=1,
                last_seen=datetime.now(),
                confidence=decision.confidence
            )

            self.learning_patterns[pattern_id] = pattern

        except Exception as e:
            logger.error(f"❌ Error creating learning pattern: {e}")

    async def _learn_from_recent_actions(self):
        """Learn patterns from recent user actions"""
        try:
            # This would analyze user's actual actions (clicks, typing, etc.)
            # For now, we'll simulate learning from decision patterns

            if len(self.decision_history) >= 10:
                recent_decisions = list(self.decision_history)[-10:]

                # Look for patterns in decision types and contexts
                context_action_pairs = []
                for decision in recent_decisions:
                    if decision.context.current_scene:
                        pair = (decision.context.current_scene.context.value, decision.action)
                        context_action_pairs.append(pair)

                # Count frequency of context-action pairs
                pair_counts = defaultdict(int)
                for pair in context_action_pairs:
                    pair_counts[pair] += 1

                # Create patterns for frequently occurring pairs
                for (context, action), count in pair_counts.items():
                    if count >= 3:  # Appeared 3+ times
                        await self._reinforce_pattern(context, action, count)

        except Exception as e:
            logger.error(f"❌ Error learning from recent actions: {e}")

    async def _reinforce_pattern(self, context: str, action: str, frequency: int):
        """Reinforce an existing pattern or create a new one"""
        try:
            # Look for existing similar pattern
            for pattern in self.learning_patterns.values():
                if (pattern.trigger_conditions.get('context') == context and
                    action in pattern.typical_actions):

                    # Reinforce existing pattern
                    pattern.frequency += frequency
                    pattern.confidence = min(0.95, pattern.confidence + 0.1)
                    pattern.last_seen = datetime.now()
                    return

            # Create new pattern if none exists
            pattern_id = f"reinforced_{context}_{action}_{int(time.time())}"
            pattern = LearningPattern(
                pattern_id=pattern_id,
                trigger_conditions={'context': context},
                typical_actions=[action],
                success_rate=0.8,  # Start with good success rate
                frequency=frequency,
                last_seen=datetime.now(),
                confidence=0.7
            )

            self.learning_patterns[pattern_id] = pattern

        except Exception as e:
            logger.error(f"❌ Error reinforcing pattern: {e}")

    def _context_changed(self, current_scene: VisualScene) -> bool:
        """Check if context has changed significantly"""
        try:
            if not self.current_context or not self.current_context.current_scene:
                return True

            previous_scene = self.current_context.current_scene

            # Check if context type changed
            if current_scene.context != previous_scene.context:
                return True

            # Check if number of elements changed significantly
            current_count = len(current_scene.elements)
            previous_count = len(previous_scene.elements)

            if abs(current_count - previous_count) > max(5, previous_count * 0.3):
                return True

            # Check if element types changed significantly
            current_types = {e.element_type for e in current_scene.elements}
            previous_types = {e.element_type for e in previous_scene.elements}

            type_difference = len(current_types.symmetric_difference(previous_types))
            if type_difference > 3:
                return True

            return False

        except Exception as e:
            return True  # Assume changed if error

    async def _analyze_context_change(self, new_scene: VisualScene):
        """Analyze what changed in the context"""
        try:
            if not self.current_context or not self.current_context.current_scene:
                return

            previous_scene = self.current_context.current_scene

            # Log significant context changes
            if new_scene.context != previous_scene.context:
                logger.info(f"🔄 Context changed: {previous_scene.context.value} → {new_scene.context.value}")

                # Generate decisions for context change
                context_decisions = await self._generate_context_change_decisions(previous_scene, new_scene)
                for decision in context_decisions:
                    if decision.should_execute():
                        await self.decision_queue.put(decision)

            # Update context history
            self.context_history.append({
                'timestamp': datetime.now(),
                'previous_context': previous_scene.context.value,
                'new_context': new_scene.context.value,
                'element_change': len(new_scene.elements) - len(previous_scene.elements)
            })

        except Exception as e:
            logger.error(f"❌ Error analyzing context change: {e}")

    async def _generate_context_change_decisions(self, previous_scene: VisualScene, new_scene: VisualScene) -> List[Decision]:
        """Generate decisions based on context changes"""
        try:
            decisions = []

            # Moving from desktop to application
            if (previous_scene.context == VisualContext.DESKTOP and
                new_scene.context == VisualContext.APPLICATION):

                decision = Decision(
                    decision_id=f"app_start_{int(time.time())}",
                    decision_type=DecisionType.PROACTIVE,
                    action="offer_app_assistance",
                    reasoning="User started a new application",
                    confidence=0.7,
                    priority=ActionPriority.LOW,
                    expected_outcome="Better app experience",
                    risk_assessment={'interruption': 0.1},
                    prerequisites=[],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            # Moving to form context
            if new_scene.context == VisualContext.FORM:
                decision = Decision(
                    decision_id=f"form_start_{int(time.time())}",
                    decision_type=DecisionType.PROACTIVE,
                    action="prepare_form_assistance",
                    reasoning="User navigated to a form",
                    confidence=0.8,
                    priority=ActionPriority.MEDIUM,
                    expected_outcome="Faster form completion",
                    risk_assessment={'privacy': 0.2},
                    prerequisites=["form_analysis"],
                    timestamp=datetime.now(),
                    context=self.current_context
                )
                decisions.append(decision)

            return decisions

        except Exception as e:
            logger.error(f"❌ Error generating context change decisions: {e}")
            return []

    async def _save_user_data(self):
        """Save user preferences and learned patterns"""
        try:
            data = {
                'preferences': self.user_preferences,
                'patterns': {}
            }

            # Convert LearningPattern objects to serializable format
            for pattern_id, pattern in self.learning_patterns.items():
                data['patterns'][pattern_id] = {
                    'pattern_id': pattern.pattern_id,
                    'trigger_conditions': pattern.trigger_conditions,
                    'typical_actions': pattern.typical_actions,
                    'success_rate': pattern.success_rate,
                    'frequency': pattern.frequency,
                    'last_seen': pattern.last_seen.isoformat(),
                    'confidence': pattern.confidence
                }

            with open('zara_user_patterns.json', 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"💾 Saved {len(self.learning_patterns)} patterns and preferences")

        except Exception as e:
            logger.error(f"❌ Error saving user data: {e}")

    async def make_decision(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> Decision:
        """Make a decision based on user input and context"""
        try:
            # Create decision context
            current_scene = visual_perception.current_scene

            decision_context = DecisionContext(
                current_scene=current_scene,
                user_intent=user_input,
                recent_actions=[d.action for d in list(self.decision_history)[-5:]],
                system_state={'timestamp': datetime.now()},
                time_constraints=None,
                user_preferences=self.user_preferences,
                safety_constraints=self.safety_rules
            )

            # Analyze user intent
            intent_analysis = await self._analyze_user_intent(user_input, current_scene)

            # Generate decision based on intent
            decision = Decision(
                decision_id=f"user_request_{int(time.time())}",
                decision_type=DecisionType.REACTIVE,
                action=intent_analysis['suggested_action'],
                reasoning=intent_analysis['reasoning'],
                confidence=intent_analysis['confidence'],
                priority=ActionPriority.HIGH,  # User requests are high priority
                expected_outcome=intent_analysis['expected_outcome'],
                risk_assessment=intent_analysis['risks'],
                prerequisites=intent_analysis['prerequisites'],
                timestamp=datetime.now(),
                context=decision_context
            )

            return decision

        except Exception as e:
            logger.error(f"❌ Error making decision: {e}")
            # Return a safe default decision
            return Decision(
                decision_id=f"error_{int(time.time())}",
                decision_type=DecisionType.REACTIVE,
                action="request_clarification",
                reasoning="Error processing request",
                confidence=0.5,
                priority=ActionPriority.MEDIUM,
                expected_outcome="Better understanding of user intent",
                risk_assessment={'misunderstanding': 0.3},
                prerequisites=[],
                timestamp=datetime.now(),
                context=self.current_context
            )

    async def _analyze_user_intent(self, user_input: str, scene: Optional[VisualScene]) -> Dict[str, Any]:
        """Analyze user intent from input"""
        try:
            user_input_lower = user_input.lower()

            # Intent patterns
            if any(word in user_input_lower for word in ['fill', 'complete', 'form']):
                return {
                    'suggested_action': 'assist_form_filling',
                    'reasoning': 'User wants help with form completion',
                    'confidence': 0.8,
                    'expected_outcome': 'Completed form',
                    'risks': {'accuracy': 0.2, 'privacy': 0.1},
                    'prerequisites': ['form_data_available']
                }

            elif any(word in user_input_lower for word in ['automate', 'repeat', 'do again']):
                return {
                    'suggested_action': 'create_automation',
                    'reasoning': 'User wants to automate a task',
                    'confidence': 0.9,
                    'expected_outcome': 'Automated task execution',
                    'risks': {'error_propagation': 0.2},
                    'prerequisites': ['task_definition']
                }

            elif any(word in user_input_lower for word in ['help', 'assist', 'guide']):
                return {
                    'suggested_action': 'provide_assistance',
                    'reasoning': 'User is requesting help',
                    'confidence': 0.95,
                    'expected_outcome': 'User receives needed assistance',
                    'risks': {'misinterpretation': 0.1},
                    'prerequisites': []
                }

            else:
                return {
                    'suggested_action': 'analyze_and_respond',
                    'reasoning': 'General user request requiring analysis',
                    'confidence': 0.6,
                    'expected_outcome': 'Appropriate response to user',
                    'risks': {'misunderstanding': 0.3},
                    'prerequisites': ['intent_clarification']
                }

        except Exception as e:
            logger.error(f"❌ Error analyzing user intent: {e}")
            return {
                'suggested_action': 'request_clarification',
                'reasoning': 'Unable to understand user intent',
                'confidence': 0.3,
                'expected_outcome': 'Clarified user intent',
                'risks': {'frustration': 0.2},
                'prerequisites': []
            }

    async def get_decision_status(self) -> str:
        """Get current status of the decision engine"""
        try:
            status = (
                f"🧠 **AUTONOMOUS DECISION ENGINE STATUS**\n"
                f"🔄 Active: {'YES' if self.is_active else 'NO'}\n"
                f"📊 Decisions Made: {len(self.decision_history)}\n"
                f"🧩 Learned Patterns: {len(self.learning_patterns)}\n"
                f"⏳ Pending Decisions: {self.decision_queue.qsize()}\n"
                f"🎯 Confidence Threshold: {self.min_confidence_threshold}\n"
                f"🛡️ Safety Rules: {len(self.safety_rules)}\n"
                f"🕐 Last Update: {datetime.now().strftime('%I:%M:%S %p')}"
            )

            return status

        except Exception as e:
            return f"❌ Error getting decision status: {e}"

# Global instance
decision_engine = AutonomousDecisionEngine()
