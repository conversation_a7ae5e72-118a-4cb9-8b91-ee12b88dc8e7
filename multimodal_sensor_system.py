#!/usr/bin/env python3
"""
ZARA Multi-Modal Sensor Integration System
Created by: Ratnam Sanjay

This module provides <PERSON><PERSON> with comprehensive environmental awareness through:
- Camera-based visual perception and face/gesture recognition
- Microphone-based audio analysis and ambient sound detection
- Screen capture and UI state monitoring
- System sensor integration (CPU, memory, network, etc.)
- Environmental context awareness
- Multi-sensor data fusion for enhanced understanding
"""

import cv2
import numpy as np
import asyncio
import threading
import time
import json
import logging
import pyaudio
import wave
import psutil
import platform
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import pyautogui
import subprocess
from collections import deque
import librosa
import soundfile as sf

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SensorType(Enum):
    """Types of sensors available"""
    CAMERA = "camera"
    MICROPHONE = "microphone"
    SCREEN = "screen"
    SYSTEM = "system"
    NETWORK = "network"
    ENVIRONMENT = "environment"

class SensorStatus(Enum):
    """Status of individual sensors"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    UNAVAILABLE = "unavailable"

@dataclass
class SensorReading:
    """Individual sensor reading"""
    sensor_type: SensorType
    timestamp: datetime
    data: Dict[str, Any]
    confidence: float
    metadata: Dict[str, Any]

@dataclass
class EnvironmentalContext:
    """Complete environmental context from all sensors"""
    timestamp: datetime
    visual_context: Dict[str, Any]
    audio_context: Dict[str, Any]
    system_context: Dict[str, Any]
    user_presence: bool
    activity_level: float
    attention_indicators: List[str]
    environmental_factors: Dict[str, Any]

class CameraSensor:
    """Camera-based visual sensor"""
    
    def __init__(self):
        self.is_active = False
        self.camera = None
        self.face_cascade = None
        self.last_frame = None
        self.face_detected = False
        self.gesture_history = deque(maxlen=10)
        
    async def initialize(self) -> bool:
        """Initialize camera sensor"""
        try:
            # Initialize camera
            self.camera = cv2.VideoCapture(0)
            if not self.camera.isOpened():
                logger.error("❌ Camera not available")
                return False
            
            # Load face detection cascade
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            except Exception as e:
                logger.warning(f"⚠️ Face detection not available: {e}")
            
            self.is_active = True
            logger.info("✅ Camera sensor initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize camera: {e}")
            return False
    
    async def get_reading(self) -> Optional[SensorReading]:
        """Get current camera reading"""
        try:
            if not self.is_active or not self.camera:
                return None
            
            ret, frame = self.camera.read()
            if not ret:
                return None
            
            self.last_frame = frame
            
            # Analyze frame
            analysis = await self._analyze_frame(frame)
            
            return SensorReading(
                sensor_type=SensorType.CAMERA,
                timestamp=datetime.now(),
                data=analysis,
                confidence=analysis.get('confidence', 0.7),
                metadata={'frame_shape': frame.shape}
            )
            
        except Exception as e:
            logger.error(f"❌ Camera reading error: {e}")
            return None
    
    async def _analyze_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """Analyze camera frame"""
        try:
            analysis = {
                'user_present': False,
                'face_detected': False,
                'face_count': 0,
                'movement_detected': False,
                'brightness_level': 0.0,
                'confidence': 0.7
            }
            
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Analyze brightness
            analysis['brightness_level'] = np.mean(gray) / 255.0
            
            # Face detection
            if self.face_cascade is not None:
                faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
                analysis['face_count'] = len(faces)
                analysis['face_detected'] = len(faces) > 0
                analysis['user_present'] = len(faces) > 0
                
                if len(faces) > 0:
                    # Get face positions for gesture analysis
                    face_positions = [(x, y, w, h) for (x, y, w, h) in faces]
                    analysis['face_positions'] = face_positions
                    
                    # Simple gesture detection (head movement)
                    gesture = await self._detect_gestures(face_positions)
                    if gesture:
                        analysis['gesture'] = gesture
            
            # Movement detection
            if hasattr(self, 'previous_frame'):
                diff = cv2.absdiff(gray, self.previous_frame)
                movement_score = np.mean(diff)
                analysis['movement_detected'] = movement_score > 10
                analysis['movement_score'] = movement_score
            
            self.previous_frame = gray
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Frame analysis error: {e}")
            return {'confidence': 0.0}
    
    async def _detect_gestures(self, face_positions: List[Tuple[int, int, int, int]]) -> Optional[str]:
        """Simple gesture detection based on face movement"""
        try:
            if not face_positions:
                return None
            
            # Store face position for gesture tracking
            current_pos = face_positions[0]  # Use first face
            self.gesture_history.append(current_pos)
            
            if len(self.gesture_history) < 5:
                return None
            
            # Analyze movement pattern
            positions = list(self.gesture_history)
            x_movement = positions[-1][0] - positions[0][0]
            y_movement = positions[-1][1] - positions[0][1]
            
            # Simple gesture recognition
            if abs(x_movement) > 30:
                return "head_shake" if x_movement > 0 else "head_turn"
            elif abs(y_movement) > 20:
                return "head_nod" if y_movement > 0 else "head_up"
            
            return None
            
        except Exception as e:
            return None
    
    def cleanup(self):
        """Cleanup camera resources"""
        try:
            if self.camera:
                self.camera.release()
            self.is_active = False
        except Exception as e:
            logger.error(f"❌ Camera cleanup error: {e}")

class AudioSensor:
    """Microphone-based audio sensor"""
    
    def __init__(self):
        self.is_active = False
        self.audio = None
        self.stream = None
        self.sample_rate = 44100
        self.chunk_size = 1024
        self.audio_buffer = deque(maxlen=100)
        
    async def initialize(self) -> bool:
        """Initialize audio sensor"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # Test microphone availability
            try:
                self.stream = self.audio.open(
                    format=pyaudio.paInt16,
                    channels=1,
                    rate=self.sample_rate,
                    input=True,
                    frames_per_buffer=self.chunk_size
                )
                self.is_active = True
                logger.info("✅ Audio sensor initialized")
                return True
                
            except Exception as e:
                logger.error(f"❌ Microphone not available: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize audio: {e}")
            return False
    
    async def get_reading(self) -> Optional[SensorReading]:
        """Get current audio reading"""
        try:
            if not self.is_active or not self.stream:
                return None
            
            # Read audio data
            data = self.stream.read(self.chunk_size, exception_on_overflow=False)
            audio_array = np.frombuffer(data, dtype=np.int16)
            
            # Store in buffer for analysis
            self.audio_buffer.append(audio_array)
            
            # Analyze audio
            analysis = await self._analyze_audio(audio_array)
            
            return SensorReading(
                sensor_type=SensorType.MICROPHONE,
                timestamp=datetime.now(),
                data=analysis,
                confidence=analysis.get('confidence', 0.8),
                metadata={'sample_rate': self.sample_rate, 'chunk_size': self.chunk_size}
            )
            
        except Exception as e:
            logger.error(f"❌ Audio reading error: {e}")
            return None
    
    async def _analyze_audio(self, audio_data: np.ndarray) -> Dict[str, Any]:
        """Analyze audio data"""
        try:
            analysis = {
                'volume_level': 0.0,
                'speech_detected': False,
                'music_detected': False,
                'silence_detected': True,
                'dominant_frequency': 0.0,
                'confidence': 0.8
            }
            
            # Calculate volume level
            volume = np.sqrt(np.mean(audio_data**2))
            analysis['volume_level'] = min(volume / 1000.0, 1.0)  # Normalize
            
            # Silence detection
            analysis['silence_detected'] = volume < 100
            
            # Simple frequency analysis
            if len(audio_data) > 0:
                fft = np.fft.fft(audio_data)
                freqs = np.fft.fftfreq(len(fft), 1/self.sample_rate)
                dominant_freq_idx = np.argmax(np.abs(fft))
                analysis['dominant_frequency'] = abs(freqs[dominant_freq_idx])
                
                # Simple speech detection (human voice frequency range)
                if 80 <= analysis['dominant_frequency'] <= 1000 and volume > 200:
                    analysis['speech_detected'] = True
                
                # Simple music detection (broader frequency range with consistent volume)
                if volume > 150 and len(self.audio_buffer) > 10:
                    recent_volumes = [np.sqrt(np.mean(chunk**2)) for chunk in list(self.audio_buffer)[-10:]]
                    volume_consistency = np.std(recent_volumes) / np.mean(recent_volumes) if np.mean(recent_volumes) > 0 else 1
                    if volume_consistency < 0.5:  # Consistent volume suggests music
                        analysis['music_detected'] = True
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Audio analysis error: {e}")
            return {'confidence': 0.0}
    
    def cleanup(self):
        """Cleanup audio resources"""
        try:
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            if self.audio:
                self.audio.terminate()
            self.is_active = False
        except Exception as e:
            logger.error(f"❌ Audio cleanup error: {e}")

class SystemSensor:
    """System resource and state sensor"""
    
    def __init__(self):
        self.is_active = True  # System sensors are always available
        
    async def get_reading(self) -> Optional[SensorReading]:
        """Get current system reading"""
        try:
            analysis = await self._analyze_system_state()
            
            return SensorReading(
                sensor_type=SensorType.SYSTEM,
                timestamp=datetime.now(),
                data=analysis,
                confidence=0.95,  # System data is highly reliable
                metadata={'platform': platform.system()}
            )
            
        except Exception as e:
            logger.error(f"❌ System reading error: {e}")
            return None

    async def _analyze_system_state(self) -> Dict[str, Any]:
        """Analyze current system state"""
        try:
            analysis = {
                'cpu_usage': psutil.cpu_percent(interval=0.1),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'network_active': False,
                'active_processes': len(psutil.pids()),
                'system_load': 'normal',
                'user_activity': 'unknown',
                'confidence': 0.95
            }

            # Network activity
            net_io = psutil.net_io_counters()
            if hasattr(self, 'previous_net_io'):
                bytes_sent_diff = net_io.bytes_sent - self.previous_net_io.bytes_sent
                bytes_recv_diff = net_io.bytes_recv - self.previous_net_io.bytes_recv
                analysis['network_active'] = (bytes_sent_diff + bytes_recv_diff) > 1024  # 1KB threshold
            self.previous_net_io = net_io

            # System load assessment
            if analysis['cpu_usage'] > 80 or analysis['memory_usage'] > 85:
                analysis['system_load'] = 'high'
            elif analysis['cpu_usage'] > 50 or analysis['memory_usage'] > 60:
                analysis['system_load'] = 'medium'
            else:
                analysis['system_load'] = 'low'

            # User activity detection (simplified)
            try:
                # Check for recent mouse/keyboard activity
                import win32api
                last_input_info = win32api.GetLastInputInfo()
                current_time = win32api.GetTickCount()
                idle_time = (current_time - last_input_info) / 1000.0  # Convert to seconds

                if idle_time < 30:
                    analysis['user_activity'] = 'active'
                elif idle_time < 300:  # 5 minutes
                    analysis['user_activity'] = 'idle'
                else:
                    analysis['user_activity'] = 'away'

                analysis['idle_time'] = idle_time

            except ImportError:
                # Fallback for non-Windows systems
                analysis['user_activity'] = 'unknown'

            # Battery status (if laptop)
            try:
                battery = psutil.sensors_battery()
                if battery:
                    analysis['battery_percent'] = battery.percent
                    analysis['power_plugged'] = battery.power_plugged
            except:
                pass

            return analysis

        except Exception as e:
            logger.error(f"❌ System analysis error: {e}")
            return {'confidence': 0.0}

class MultiModalSensorSystem:
    """Integrated multi-modal sensor system"""

    def __init__(self):
        self.is_active = False
        self.sensors = {}
        self.sensor_readings = deque(maxlen=1000)
        self.environmental_context = None
        self.context_history = deque(maxlen=50)

        # Initialize sensors
        self.camera_sensor = CameraSensor()
        self.audio_sensor = AudioSensor()
        self.system_sensor = SystemSensor()

        # Sensor fusion parameters
        self.fusion_weights = {
            SensorType.CAMERA: 0.3,
            SensorType.MICROPHONE: 0.2,
            SensorType.SCREEN: 0.3,
            SensorType.SYSTEM: 0.2
        }

        # Context analysis
        self.context_analyzer = ContextAnalyzer()

    async def initialize_sensors(self) -> str:
        """Initialize all available sensors"""
        try:
            initialization_results = {}

            # Initialize camera
            camera_success = await self.camera_sensor.initialize()
            initialization_results['camera'] = camera_success
            if camera_success:
                self.sensors[SensorType.CAMERA] = self.camera_sensor

            # Initialize audio
            audio_success = await self.audio_sensor.initialize()
            initialization_results['audio'] = audio_success
            if audio_success:
                self.sensors[SensorType.MICROPHONE] = self.audio_sensor

            # System sensor is always available
            self.sensors[SensorType.SYSTEM] = self.system_sensor
            initialization_results['system'] = True

            self.is_active = True

            # Start sensor monitoring loop
            asyncio.create_task(self._sensor_monitoring_loop())

            result = (
                f"🔬 **MULTI-MODAL SENSOR SYSTEM ACTIVATED!**\n"
                f"📹 Camera: {'✅ ACTIVE' if initialization_results['camera'] else '❌ UNAVAILABLE'}\n"
                f"🎤 Microphone: {'✅ ACTIVE' if initialization_results['audio'] else '❌ UNAVAILABLE'}\n"
                f"💻 System Monitor: ✅ ACTIVE\n"
                f"🧠 Context Analysis: ✅ ENABLED\n"
                f"🔄 Sensor Fusion: ✅ ONLINE\n"
                f"🕐 Started: {datetime.now().strftime('%I:%M:%S %p')}"
            )

            logger.info("🔬 Multi-Modal Sensor System Started")
            return result

        except Exception as e:
            self.is_active = False
            logger.error(f"❌ Failed to initialize sensors: {e}")
            return f"❌ Failed to initialize sensors: {e}"

    async def stop_sensors(self) -> str:
        """Stop all sensors"""
        try:
            self.is_active = False

            # Cleanup sensors
            if hasattr(self, 'camera_sensor'):
                self.camera_sensor.cleanup()
            if hasattr(self, 'audio_sensor'):
                self.audio_sensor.cleanup()

            result = (
                f"🛑 **SENSOR SYSTEM STOPPED**\n"
                f"🔬 All Sensors: DISABLED\n"
                f"🧹 Resources: CLEANED UP\n"
                f"🕐 Stopped: {datetime.now().strftime('%I:%M:%S %p')}"
            )

            logger.info("🛑 Multi-Modal Sensor System Stopped")
            return result

        except Exception as e:
            logger.error(f"❌ Error stopping sensors: {e}")
            return f"❌ Error stopping sensors: {e}"

    async def _sensor_monitoring_loop(self):
        """Main sensor monitoring loop"""
        while self.is_active:
            try:
                # Collect readings from all active sensors
                readings = await self._collect_sensor_readings()

                # Perform sensor fusion
                fused_context = await self._fuse_sensor_data(readings)

                # Update environmental context
                if fused_context:
                    self.environmental_context = fused_context
                    self.context_history.append(fused_context)

                # Store readings
                for reading in readings:
                    self.sensor_readings.append(reading)

                # Sleep before next iteration
                await asyncio.sleep(1.0)  # 1 Hz monitoring

            except Exception as e:
                logger.error(f"❌ Error in sensor monitoring loop: {e}")
                await asyncio.sleep(2.0)

    async def _collect_sensor_readings(self) -> List[SensorReading]:
        """Collect readings from all active sensors"""
        readings = []

        for sensor_type, sensor in self.sensors.items():
            try:
                reading = await sensor.get_reading()
                if reading:
                    readings.append(reading)
            except Exception as e:
                logger.error(f"❌ Error reading from {sensor_type.value} sensor: {e}")

        return readings

    async def _fuse_sensor_data(self, readings: List[SensorReading]) -> Optional[EnvironmentalContext]:
        """Fuse data from multiple sensors into unified context"""
        try:
            if not readings:
                return None

            # Organize readings by sensor type
            sensor_data = {}
            for reading in readings:
                sensor_data[reading.sensor_type] = reading.data

            # Extract key information
            visual_context = sensor_data.get(SensorType.CAMERA, {})
            audio_context = sensor_data.get(SensorType.MICROPHONE, {})
            system_context = sensor_data.get(SensorType.SYSTEM, {})

            # Determine user presence (fusion of multiple indicators)
            user_presence = self._determine_user_presence(visual_context, audio_context, system_context)

            # Calculate activity level
            activity_level = self._calculate_activity_level(visual_context, audio_context, system_context)

            # Identify attention indicators
            attention_indicators = self._identify_attention_indicators(visual_context, audio_context, system_context)

            # Analyze environmental factors
            environmental_factors = self._analyze_environmental_factors(visual_context, audio_context, system_context)

            return EnvironmentalContext(
                timestamp=datetime.now(),
                visual_context=visual_context,
                audio_context=audio_context,
                system_context=system_context,
                user_presence=user_presence,
                activity_level=activity_level,
                attention_indicators=attention_indicators,
                environmental_factors=environmental_factors
            )

        except Exception as e:
            logger.error(f"❌ Error fusing sensor data: {e}")
            return None

    def _determine_user_presence(self, visual: Dict, audio: Dict, system: Dict) -> bool:
        """Determine if user is present based on multiple sensors"""
        presence_indicators = []

        # Visual indicators
        if visual.get('face_detected', False):
            presence_indicators.append(0.9)
        elif visual.get('movement_detected', False):
            presence_indicators.append(0.6)

        # Audio indicators
        if audio.get('speech_detected', False):
            presence_indicators.append(0.8)
        elif audio.get('volume_level', 0) > 0.1 and not audio.get('silence_detected', True):
            presence_indicators.append(0.4)

        # System indicators
        user_activity = system.get('user_activity', 'unknown')
        if user_activity == 'active':
            presence_indicators.append(0.7)
        elif user_activity == 'idle':
            presence_indicators.append(0.3)

        # Fusion: user is present if any strong indicator or multiple weak indicators
        if not presence_indicators:
            return False

        max_confidence = max(presence_indicators)
        avg_confidence = sum(presence_indicators) / len(presence_indicators)

        return max_confidence > 0.7 or (len(presence_indicators) >= 2 and avg_confidence > 0.4)

    def _calculate_activity_level(self, visual: Dict, audio: Dict, system: Dict) -> float:
        """Calculate overall activity level (0.0 to 1.0)"""
        activity_scores = []

        # Visual activity
        if visual.get('movement_detected', False):
            activity_scores.append(visual.get('movement_score', 0) / 50.0)  # Normalize

        # Audio activity
        volume_level = audio.get('volume_level', 0)
        activity_scores.append(min(volume_level * 2, 1.0))  # Amplify and cap

        # System activity
        cpu_usage = system.get('cpu_usage', 0) / 100.0
        activity_scores.append(cpu_usage)

        user_activity = system.get('user_activity', 'unknown')
        if user_activity == 'active':
            activity_scores.append(0.8)
        elif user_activity == 'idle':
            activity_scores.append(0.3)

        return min(sum(activity_scores) / len(activity_scores) if activity_scores else 0.0, 1.0)

    def _identify_attention_indicators(self, visual: Dict, audio: Dict, system: Dict) -> List[str]:
        """Identify indicators of user attention and focus"""
        indicators = []

        # Visual attention indicators
        if visual.get('face_detected', False):
            indicators.append('user_looking_at_screen')

        gesture = visual.get('gesture')
        if gesture:
            indicators.append(f'gesture_{gesture}')

        # Audio attention indicators
        if audio.get('speech_detected', False):
            indicators.append('user_speaking')

        if audio.get('silence_detected', True) and visual.get('face_detected', False):
            indicators.append('focused_attention')

        # System attention indicators
        if system.get('user_activity') == 'active':
            indicators.append('active_interaction')

        cpu_usage = system.get('cpu_usage', 0)
        if cpu_usage > 70:
            indicators.append('intensive_task')

        return indicators

    def _analyze_environmental_factors(self, visual: Dict, audio: Dict, system: Dict) -> Dict[str, Any]:
        """Analyze environmental factors affecting user experience"""
        factors = {}

        # Lighting conditions
        brightness = visual.get('brightness_level', 0.5)
        if brightness < 0.2:
            factors['lighting'] = 'too_dark'
        elif brightness > 0.8:
            factors['lighting'] = 'too_bright'
        else:
            factors['lighting'] = 'adequate'

        # Noise levels
        volume = audio.get('volume_level', 0)
        if volume > 0.7:
            factors['noise_level'] = 'high'
        elif volume > 0.3:
            factors['noise_level'] = 'moderate'
        else:
            factors['noise_level'] = 'quiet'

        # System performance
        system_load = system.get('system_load', 'normal')
        factors['system_performance'] = system_load

        # Distraction indicators
        if audio.get('music_detected', False):
            factors['background_music'] = True

        if system.get('network_active', False):
            factors['network_activity'] = True

        return factors

    async def get_current_context(self) -> Optional[EnvironmentalContext]:
        """Get current environmental context"""
        return self.environmental_context

    async def get_sensor_status(self) -> str:
        """Get status of all sensors"""
        try:
            status_lines = ["🔬 **SENSOR SYSTEM STATUS**"]

            for sensor_type, sensor in self.sensors.items():
                if hasattr(sensor, 'is_active'):
                    status = "✅ ACTIVE" if sensor.is_active else "❌ INACTIVE"
                else:
                    status = "✅ ACTIVE"

                status_lines.append(f"{self._get_sensor_emoji(sensor_type)} {sensor_type.value.title()}: {status}")

            if self.environmental_context:
                status_lines.extend([
                    "",
                    "🌍 **CURRENT ENVIRONMENT**",
                    f"👤 User Present: {'YES' if self.environmental_context.user_presence else 'NO'}",
                    f"⚡ Activity Level: {self.environmental_context.activity_level:.1%}",
                    f"🎯 Attention: {', '.join(self.environmental_context.attention_indicators[:3])}",
                    f"🕐 Last Update: {self.environmental_context.timestamp.strftime('%I:%M:%S %p')}"
                ])

            status_lines.append(f"\n📊 Total Readings: {len(self.sensor_readings)}")

            return "\n".join(status_lines)

        except Exception as e:
            return f"❌ Error getting sensor status: {e}"

    def _get_sensor_emoji(self, sensor_type: SensorType) -> str:
        """Get emoji for sensor type"""
        emoji_map = {
            SensorType.CAMERA: "📹",
            SensorType.MICROPHONE: "🎤",
            SensorType.SCREEN: "🖥️",
            SensorType.SYSTEM: "💻",
            SensorType.NETWORK: "🌐",
            SensorType.ENVIRONMENT: "🌍"
        }
        return emoji_map.get(sensor_type, "🔬")

class ContextAnalyzer:
    """Advanced context analysis from sensor fusion"""

    def __init__(self):
        self.context_patterns = {}
        self.learning_enabled = True

    async def analyze_context_change(self, previous: EnvironmentalContext, current: EnvironmentalContext) -> Dict[str, Any]:
        """Analyze significant context changes"""
        try:
            changes = {}

            # User presence change
            if previous.user_presence != current.user_presence:
                changes['user_presence'] = {
                    'from': previous.user_presence,
                    'to': current.user_presence,
                    'significance': 'high'
                }

            # Activity level change
            activity_diff = abs(current.activity_level - previous.activity_level)
            if activity_diff > 0.3:
                changes['activity_level'] = {
                    'from': previous.activity_level,
                    'to': current.activity_level,
                    'difference': activity_diff,
                    'significance': 'medium' if activity_diff > 0.5 else 'low'
                }

            # Attention changes
            prev_attention = set(previous.attention_indicators)
            curr_attention = set(current.attention_indicators)

            new_indicators = curr_attention - prev_attention
            lost_indicators = prev_attention - curr_attention

            if new_indicators or lost_indicators:
                changes['attention'] = {
                    'new_indicators': list(new_indicators),
                    'lost_indicators': list(lost_indicators),
                    'significance': 'medium'
                }

            return changes

        except Exception as e:
            logger.error(f"❌ Error analyzing context change: {e}")
            return {}

# Global instance
sensor_system = MultiModalSensorSystem()
