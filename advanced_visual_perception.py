#!/usr/bin/env python3
"""
ZARA Advanced Visual Perception System
Created by: <PERSON>nam Sanjay

This module provides <PERSON><PERSON> with human-like visual understanding:
- Real-time scene analysis and object detection
- UI element recognition and interaction mapping
- Context-aware visual reasoning
- Autonomous visual decision making
- Multi-modal visual intelligence
"""

import cv2
import numpy as np
import asyncio
import threading
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import pyautogui
import pytesseract
from PIL import Image, ImageDraw, ImageFont
import torch
import torchvision.transforms as transforms
from collections import deque
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VisualElementType(Enum):
    """Types of visual elements Z<PERSON> can recognize"""
    BUTTON = "button"
    INPUT_FIELD = "input_field"
    DROPDOWN = "dropdown"
    CHECKBOX = "checkbox"
    RADIO_BUTTON = "radio_button"
    LINK = "link"
    IMAGE = "image"
    TEXT = "text"
    ICON = "icon"
    MENU = "menu"
    WINDOW = "window"
    DIALOG = "dialog"
    NOTIFICATION = "notification"
    PROGRESS_BAR = "progress_bar"
    SLIDER = "slider"
    TAB = "tab"
    UNKNOWN = "unknown"

class VisualContext(Enum):
    """Different visual contexts Zara can understand"""
    DESKTOP = "desktop"
    BROWSER = "browser"
    APPLICATION = "application"
    GAME = "game"
    VIDEO = "video"
    DOCUMENT = "document"
    FORM = "form"
    SETTINGS = "settings"
    FILE_MANAGER = "file_manager"
    UNKNOWN = "unknown"

@dataclass
class VisualElement:
    """Represents a visual element detected on screen"""
    element_type: VisualElementType
    bounds: Tuple[int, int, int, int]  # x, y, width, height
    confidence: float
    text: Optional[str] = None
    attributes: Dict[str, Any] = None
    clickable: bool = False
    interactive: bool = False
    context: Optional[str] = None
    
    def center(self) -> Tuple[int, int]:
        """Get center point of element"""
        x, y, w, h = self.bounds
        return (x + w // 2, y + h // 2)
    
    def area(self) -> int:
        """Get area of element"""
        _, _, w, h = self.bounds
        return w * h

@dataclass
class VisualScene:
    """Represents the complete visual understanding of current screen"""
    timestamp: datetime
    context: VisualContext
    elements: List[VisualElement]
    dominant_colors: List[Tuple[int, int, int]]
    text_content: str
    layout_structure: Dict[str, Any]
    attention_areas: List[Tuple[int, int, int, int]]  # Areas of visual interest
    change_regions: List[Tuple[int, int, int, int]]  # Areas that changed
    confidence: float
    
    def get_elements_by_type(self, element_type: VisualElementType) -> List[VisualElement]:
        """Get all elements of specific type"""
        return [elem for elem in self.elements if elem.element_type == element_type]
    
    def get_clickable_elements(self) -> List[VisualElement]:
        """Get all clickable elements"""
        return [elem for elem in self.elements if elem.clickable]
    
    def find_element_by_text(self, text: str, fuzzy: bool = True) -> Optional[VisualElement]:
        """Find element containing specific text"""
        text_lower = text.lower()
        for elem in self.elements:
            if elem.text:
                elem_text = elem.text.lower()
                if fuzzy:
                    if text_lower in elem_text or elem_text in text_lower:
                        return elem
                else:
                    if text_lower == elem_text:
                        return elem
        return None

class AdvancedVisualPerception:
    """Advanced visual perception system for Zara"""
    
    def __init__(self):
        self.is_active = False
        self.current_scene: Optional[VisualScene] = None
        self.scene_history: deque = deque(maxlen=10)
        self.last_screenshot = None
        self.last_screenshot_hash = None
        
        # Visual processing settings
        self.fps = 2  # Frames per second for analysis
        self.change_threshold = 0.05  # Minimum change to trigger analysis
        self.confidence_threshold = 0.7
        
        # Initialize computer vision models
        self._initialize_cv_models()
        
        # Element detection patterns
        self._initialize_detection_patterns()
        
        # Visual memory for learning
        self.visual_memory = {}
        self.interaction_patterns = {}
        
    def _initialize_cv_models(self):
        """Initialize computer vision models"""
        try:
            # Initialize OCR
            self.ocr_available = True
            
            # Initialize basic CV components
            self.edge_detector = cv2.Canny
            self.contour_detector = cv2.findContours
            
            # Color analysis
            self.color_analyzer = cv2.calcHist
            
            logger.info("✅ Computer vision models initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize CV models: {e}")
            self.ocr_available = False
    
    def _initialize_detection_patterns(self):
        """Initialize patterns for detecting UI elements"""
        self.ui_patterns = {
            'button': {
                'color_ranges': [
                    ([200, 200, 200], [255, 255, 255]),  # Light buttons
                    ([100, 100, 100], [180, 180, 180]),  # Gray buttons
                ],
                'size_range': (20, 200),  # Min/max width
                'aspect_ratio': (0.2, 5.0),  # Width/height ratio
                'text_indicators': ['click', 'submit', 'ok', 'cancel', 'apply']
            },
            'input_field': {
                'color_ranges': [
                    ([240, 240, 240], [255, 255, 255]),  # White backgrounds
                ],
                'size_range': (50, 500),
                'aspect_ratio': (2.0, 20.0),  # Usually wider than tall
                'border_indicators': True
            },
            'dropdown': {
                'indicators': ['▼', '⌄', 'dropdown', 'select'],
                'size_range': (80, 300),
                'aspect_ratio': (2.0, 10.0)
            }
        }
    
    async def start_visual_perception(self) -> str:
        """Start the advanced visual perception system"""
        try:
            if self.is_active:
                return "🔥 Advanced Visual Perception already active!"
            
            self.is_active = True
            
            # Start perception loop in background
            asyncio.create_task(self._perception_loop())
            
            result = (
                f"🔥 **ADVANCED VISUAL PERCEPTION ACTIVATED!**\n"
                f"👁️ Real-time Scene Analysis: ACTIVE\n"
                f"🎯 UI Element Detection: ENABLED\n"
                f"🧠 Visual Reasoning: ONLINE\n"
                f"📊 Processing Rate: {self.fps} FPS\n"
                f"🕐 Started: {datetime.now().strftime('%I:%M:%S %p')}"
            )
            
            logger.info("🔥 Advanced Visual Perception System Started")
            return result
            
        except Exception as e:
            self.is_active = False
            logger.error(f"❌ Failed to start visual perception: {e}")
            return f"❌ Failed to start visual perception: {e}"
    
    async def stop_visual_perception(self) -> str:
        """Stop the visual perception system"""
        try:
            self.is_active = False
            
            result = (
                f"🛑 **VISUAL PERCEPTION STOPPED**\n"
                f"👁️ Scene Analysis: DISABLED\n"
                f"🧹 Memory Cleanup: COMPLETE\n"
                f"🕐 Stopped: {datetime.now().strftime('%I:%M:%S %p')}"
            )
            
            logger.info("🛑 Visual Perception System Stopped")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error stopping visual perception: {e}")
            return f"❌ Error stopping visual perception: {e}"
    
    async def _perception_loop(self):
        """Main perception processing loop"""
        while self.is_active:
            try:
                # Capture and analyze current screen
                await self._analyze_current_screen()
                
                # Sleep based on FPS setting
                await asyncio.sleep(1.0 / self.fps)
                
            except Exception as e:
                logger.error(f"❌ Error in perception loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _analyze_current_screen(self):
        """Analyze current screen and update visual understanding"""
        try:
            # Take screenshot
            screenshot = await asyncio.to_thread(pyautogui.screenshot)
            screenshot_np = np.array(screenshot)
            
            # Check if screen changed significantly
            if not await self._screen_changed(screenshot_np):
                return
            
            # Detect visual elements
            elements = await self._detect_visual_elements(screenshot_np)
            
            # Analyze context
            context = await self._analyze_visual_context(screenshot_np, elements)
            
            # Extract text content
            text_content = await self._extract_text_content(screenshot_np)
            
            # Analyze layout and structure
            layout = await self._analyze_layout_structure(elements)
            
            # Identify attention areas
            attention_areas = await self._identify_attention_areas(screenshot_np)
            
            # Calculate dominant colors
            dominant_colors = await self._analyze_dominant_colors(screenshot_np)
            
            # Create visual scene
            scene = VisualScene(
                timestamp=datetime.now(),
                context=context,
                elements=elements,
                dominant_colors=dominant_colors,
                text_content=text_content,
                layout_structure=layout,
                attention_areas=attention_areas,
                change_regions=[],  # Will be populated by change detection
                confidence=0.85
            )
            
            # Update current scene and history
            if self.current_scene:
                self.scene_history.append(self.current_scene)
            
            self.current_scene = scene
            self.last_screenshot = screenshot_np
            
            # Learn from interaction patterns
            await self._learn_from_scene(scene)
            
        except Exception as e:
            logger.error(f"❌ Error analyzing screen: {e}")
    
    async def _screen_changed(self, screenshot_np: np.ndarray) -> bool:
        """Check if screen changed significantly"""
        try:
            if self.last_screenshot is None:
                return True
            
            # Calculate image hash for quick comparison
            current_hash = hashlib.md5(screenshot_np.tobytes()).hexdigest()
            
            if self.last_screenshot_hash == current_hash:
                return False
            
            self.last_screenshot_hash = current_hash
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking screen change: {e}")
            return True

    async def _detect_visual_elements(self, screenshot_np: np.ndarray) -> List[VisualElement]:
        """Detect all visual elements on screen"""
        try:
            elements = []

            # Convert to different color spaces
            gray = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2GRAY)
            hsv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2HSV)

            # Detect buttons
            button_elements = await self._detect_buttons(screenshot_np, gray)
            elements.extend(button_elements)

            # Detect input fields
            input_elements = await self._detect_input_fields(screenshot_np, gray)
            elements.extend(input_elements)

            # Detect text elements
            text_elements = await self._detect_text_elements(screenshot_np)
            elements.extend(text_elements)

            # Detect interactive elements
            interactive_elements = await self._detect_interactive_elements(screenshot_np, gray)
            elements.extend(interactive_elements)

            # Remove duplicates and merge overlapping elements
            elements = await self._merge_overlapping_elements(elements)

            return elements

        except Exception as e:
            logger.error(f"❌ Error detecting visual elements: {e}")
            return []

    async def _detect_buttons(self, screenshot_np: np.ndarray, gray: np.ndarray) -> List[VisualElement]:
        """Detect button elements"""
        try:
            buttons = []

            # Edge detection for rectangular shapes
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)

                # Filter by size and aspect ratio
                if (20 <= w <= 200 and 15 <= h <= 60 and
                    0.2 <= w/h <= 5.0 and cv2.contourArea(contour) > 300):

                    # Extract region for text analysis
                    region = screenshot_np[y:y+h, x:x+w]

                    # Check if it looks like a button
                    if await self._is_button_like(region):
                        # Extract text if available
                        text = await self._extract_text_from_region(region)

                        button = VisualElement(
                            element_type=VisualElementType.BUTTON,
                            bounds=(x, y, w, h),
                            confidence=0.8,
                            text=text,
                            clickable=True,
                            interactive=True,
                            attributes={'contour_area': cv2.contourArea(contour)}
                        )
                        buttons.append(button)

            return buttons

        except Exception as e:
            logger.error(f"❌ Error detecting buttons: {e}")
            return []

    async def _detect_input_fields(self, screenshot_np: np.ndarray, gray: np.ndarray) -> List[VisualElement]:
        """Detect input field elements"""
        try:
            input_fields = []

            # Look for white/light rectangular areas (typical input fields)
            # Create mask for light colors
            lower_white = np.array([240, 240, 240])
            upper_white = np.array([255, 255, 255])
            mask = cv2.inRange(screenshot_np, lower_white, upper_white)

            # Find contours in the mask
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # Filter for input field characteristics
                if (50 <= w <= 500 and 15 <= h <= 50 and
                    2.0 <= w/h <= 20.0 and cv2.contourArea(contour) > 500):

                    # Check for border (input fields usually have borders)
                    region = gray[max(0, y-2):min(gray.shape[0], y+h+2),
                                max(0, x-2):min(gray.shape[1], x+w+2)]

                    if await self._has_border(region):
                        # Extract any placeholder or existing text
                        text_region = screenshot_np[y:y+h, x:x+w]
                        text = await self._extract_text_from_region(text_region)

                        input_field = VisualElement(
                            element_type=VisualElementType.INPUT_FIELD,
                            bounds=(x, y, w, h),
                            confidence=0.75,
                            text=text,
                            clickable=True,
                            interactive=True,
                            attributes={'has_border': True}
                        )
                        input_fields.append(input_field)

            return input_fields

        except Exception as e:
            logger.error(f"❌ Error detecting input fields: {e}")
            return []

    async def _detect_text_elements(self, screenshot_np: np.ndarray) -> List[VisualElement]:
        """Detect text elements using OCR"""
        try:
            if not self.ocr_available:
                return []

            text_elements = []

            # Use OCR to detect text regions
            try:
                # Get detailed OCR data
                ocr_data = pytesseract.image_to_data(screenshot_np, output_type=pytesseract.Output.DICT)

                for i in range(len(ocr_data['text'])):
                    text = ocr_data['text'][i].strip()
                    confidence = int(ocr_data['conf'][i])

                    if text and confidence > 30:  # Filter low confidence text
                        x = ocr_data['left'][i]
                        y = ocr_data['top'][i]
                        w = ocr_data['width'][i]
                        h = ocr_data['height'][i]

                        # Skip very small text regions
                        if w > 10 and h > 10:
                            text_element = VisualElement(
                                element_type=VisualElementType.TEXT,
                                bounds=(x, y, w, h),
                                confidence=confidence / 100.0,
                                text=text,
                                clickable=False,
                                interactive=False,
                                attributes={'ocr_confidence': confidence}
                            )
                            text_elements.append(text_element)

            except Exception as ocr_error:
                logger.warning(f"⚠️ OCR processing failed: {ocr_error}")

            return text_elements

        except Exception as e:
            logger.error(f"❌ Error detecting text elements: {e}")
            return []

    async def _detect_interactive_elements(self, screenshot_np: np.ndarray, gray: np.ndarray) -> List[VisualElement]:
        """Detect other interactive elements like dropdowns, checkboxes, etc."""
        try:
            interactive_elements = []

            # Detect dropdown indicators
            dropdown_elements = await self._detect_dropdowns(screenshot_np)
            interactive_elements.extend(dropdown_elements)

            # Detect checkboxes and radio buttons
            checkbox_elements = await self._detect_checkboxes(gray)
            interactive_elements.extend(checkbox_elements)

            return interactive_elements

        except Exception as e:
            logger.error(f"❌ Error detecting interactive elements: {e}")
            return []

    async def _detect_dropdowns(self, screenshot_np: np.ndarray) -> List[VisualElement]:
        """Detect dropdown elements"""
        try:
            dropdowns = []

            # Look for dropdown indicators (▼, ⌄, etc.)
            dropdown_indicators = ['▼', '⌄', '▽', '∨']

            # Use OCR to find dropdown indicators
            if self.ocr_available:
                try:
                    ocr_data = pytesseract.image_to_data(screenshot_np, output_type=pytesseract.Output.DICT)

                    for i in range(len(ocr_data['text'])):
                        text = ocr_data['text'][i].strip()

                        if any(indicator in text for indicator in dropdown_indicators):
                            x = ocr_data['left'][i]
                            y = ocr_data['top'][i]
                            w = max(ocr_data['width'][i], 100)  # Expand to include dropdown area
                            h = max(ocr_data['height'][i], 25)

                            dropdown = VisualElement(
                                element_type=VisualElementType.DROPDOWN,
                                bounds=(x, y, w, h),
                                confidence=0.7,
                                text=text,
                                clickable=True,
                                interactive=True,
                                attributes={'indicator_found': True}
                            )
                            dropdowns.append(dropdown)

                except Exception as ocr_error:
                    logger.warning(f"⚠️ Dropdown OCR failed: {ocr_error}")

            return dropdowns

        except Exception as e:
            logger.error(f"❌ Error detecting dropdowns: {e}")
            return []

    async def _detect_checkboxes(self, gray: np.ndarray) -> List[VisualElement]:
        """Detect checkbox and radio button elements"""
        try:
            checkboxes = []

            # Look for small square regions (checkboxes)
            # Use template matching for common checkbox patterns
            checkbox_size = 15

            # Create simple checkbox templates
            checkbox_template = np.zeros((checkbox_size, checkbox_size), dtype=np.uint8)
            cv2.rectangle(checkbox_template, (1, 1), (checkbox_size-2, checkbox_size-2), 255, 1)

            # Template matching
            result = cv2.matchTemplate(gray, checkbox_template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= 0.6)

            for pt in zip(*locations[::-1]):
                x, y = pt
                w, h = checkbox_size, checkbox_size

                checkbox = VisualElement(
                    element_type=VisualElementType.CHECKBOX,
                    bounds=(x, y, w, h),
                    confidence=0.6,
                    text=None,
                    clickable=True,
                    interactive=True,
                    attributes={'template_match': True}
                )
                checkboxes.append(checkbox)

            return checkboxes

        except Exception as e:
            logger.error(f"❌ Error detecting checkboxes: {e}")
            return []

    async def _is_button_like(self, region: np.ndarray) -> bool:
        """Check if a region looks like a button"""
        try:
            # Check for uniform color (buttons often have solid backgrounds)
            gray_region = cv2.cvtColor(region, cv2.COLOR_RGB2GRAY)
            std_dev = np.std(gray_region)

            # Buttons typically have low standard deviation (uniform color)
            if std_dev < 30:
                return True

            # Check for text content
            if self.ocr_available:
                try:
                    text = pytesseract.image_to_string(region).strip()
                    button_keywords = ['click', 'submit', 'ok', 'cancel', 'apply', 'save', 'delete', 'edit']
                    if any(keyword in text.lower() for keyword in button_keywords):
                        return True
                except:
                    pass

            return False

        except Exception as e:
            return False

    async def _has_border(self, region: np.ndarray) -> bool:
        """Check if a region has a border (typical of input fields)"""
        try:
            if region.size == 0:
                return False

            # Check edges for consistent lines
            edges = cv2.Canny(region, 50, 150)

            # Count edge pixels on borders
            top_edge = np.sum(edges[0, :])
            bottom_edge = np.sum(edges[-1, :])
            left_edge = np.sum(edges[:, 0])
            right_edge = np.sum(edges[:, -1])

            # If multiple edges have significant edge pixels, likely has border
            edge_count = sum([top_edge > 50, bottom_edge > 50, left_edge > 50, right_edge > 50])

            return edge_count >= 2

        except Exception as e:
            return False

    async def _extract_text_from_region(self, region: np.ndarray) -> Optional[str]:
        """Extract text from a specific region"""
        try:
            if not self.ocr_available or region.size == 0:
                return None

            # Preprocess region for better OCR
            if len(region.shape) == 3:
                gray_region = cv2.cvtColor(region, cv2.COLOR_RGB2GRAY)
            else:
                gray_region = region

            # Enhance contrast
            enhanced = cv2.equalizeHist(gray_region)

            # Extract text
            text = pytesseract.image_to_string(enhanced, config='--psm 8').strip()

            return text if text else None

        except Exception as e:
            return None

    async def _merge_overlapping_elements(self, elements: List[VisualElement]) -> List[VisualElement]:
        """Merge overlapping elements to avoid duplicates"""
        try:
            if not elements:
                return elements

            merged = []
            used_indices = set()

            for i, elem1 in enumerate(elements):
                if i in used_indices:
                    continue

                # Find overlapping elements
                overlapping = [elem1]
                used_indices.add(i)

                for j, elem2 in enumerate(elements[i+1:], i+1):
                    if j in used_indices:
                        continue

                    if self._elements_overlap(elem1, elem2):
                        overlapping.append(elem2)
                        used_indices.add(j)

                # Merge overlapping elements
                if len(overlapping) == 1:
                    merged.append(overlapping[0])
                else:
                    merged_element = self._merge_elements(overlapping)
                    merged.append(merged_element)

            return merged

        except Exception as e:
            logger.error(f"❌ Error merging elements: {e}")
            return elements

    def _elements_overlap(self, elem1: VisualElement, elem2: VisualElement) -> bool:
        """Check if two elements overlap significantly"""
        x1, y1, w1, h1 = elem1.bounds
        x2, y2, w2, h2 = elem2.bounds

        # Calculate intersection
        left = max(x1, x2)
        top = max(y1, y2)
        right = min(x1 + w1, x2 + w2)
        bottom = min(y1 + h1, y2 + h2)

        if left < right and top < bottom:
            intersection_area = (right - left) * (bottom - top)
            area1 = w1 * h1
            area2 = w2 * h2

            # Check if intersection is significant (>50% of smaller element)
            overlap_ratio = intersection_area / min(area1, area2)
            return overlap_ratio > 0.5

        return False

    def _merge_elements(self, elements: List[VisualElement]) -> VisualElement:
        """Merge multiple overlapping elements into one"""
        # Find bounding box that contains all elements
        min_x = min(elem.bounds[0] for elem in elements)
        min_y = min(elem.bounds[1] for elem in elements)
        max_x = max(elem.bounds[0] + elem.bounds[2] for elem in elements)
        max_y = max(elem.bounds[1] + elem.bounds[3] for elem in elements)

        # Choose the element type with highest confidence
        best_element = max(elements, key=lambda e: e.confidence)

        # Combine text from all elements
        texts = [elem.text for elem in elements if elem.text]
        combined_text = ' '.join(texts) if texts else None

        return VisualElement(
            element_type=best_element.element_type,
            bounds=(min_x, min_y, max_x - min_x, max_y - min_y),
            confidence=best_element.confidence,
            text=combined_text,
            clickable=any(elem.clickable for elem in elements),
            interactive=any(elem.interactive for elem in elements),
            attributes={'merged_from': len(elements)}
        )

    async def _analyze_visual_context(self, screenshot_np: np.ndarray, elements: List[VisualElement]) -> VisualContext:
        """Analyze the visual context of the current screen"""
        try:
            # Analyze based on elements present
            button_count = len([e for e in elements if e.element_type == VisualElementType.BUTTON])
            input_count = len([e for e in elements if e.element_type == VisualElementType.INPUT_FIELD])
            text_count = len([e for e in elements if e.element_type == VisualElementType.TEXT])

            # Extract all text for context analysis
            all_text = ' '.join([e.text for e in elements if e.text]).lower()

            # Context detection based on content
            if 'browser' in all_text or 'http' in all_text or 'www' in all_text:
                return VisualContext.BROWSER
            elif input_count > 3 and button_count > 0:
                return VisualContext.FORM
            elif 'file' in all_text and 'folder' in all_text:
                return VisualContext.FILE_MANAGER
            elif 'settings' in all_text or 'preferences' in all_text:
                return VisualContext.SETTINGS
            elif 'document' in all_text or text_count > 10:
                return VisualContext.DOCUMENT
            elif button_count > 5:
                return VisualContext.APPLICATION
            else:
                return VisualContext.DESKTOP

        except Exception as e:
            logger.error(f"❌ Error analyzing visual context: {e}")
            return VisualContext.UNKNOWN

    async def _extract_text_content(self, screenshot_np: np.ndarray) -> str:
        """Extract all text content from screen"""
        try:
            if not self.ocr_available:
                return ""

            # Use OCR to extract all text
            text = pytesseract.image_to_string(screenshot_np)
            return text.strip()

        except Exception as e:
            logger.error(f"❌ Error extracting text content: {e}")
            return ""

    async def _analyze_layout_structure(self, elements: List[VisualElement]) -> Dict[str, Any]:
        """Analyze the layout structure of elements"""
        try:
            layout = {
                'total_elements': len(elements),
                'element_types': {},
                'spatial_distribution': {},
                'interaction_zones': []
            }

            # Count element types
            for element in elements:
                elem_type = element.element_type.value
                layout['element_types'][elem_type] = layout['element_types'].get(elem_type, 0) + 1

            # Analyze spatial distribution
            if elements:
                x_coords = [e.bounds[0] for e in elements]
                y_coords = [e.bounds[1] for e in elements]

                layout['spatial_distribution'] = {
                    'left_heavy': sum(1 for x in x_coords if x < 500) / len(x_coords),
                    'top_heavy': sum(1 for y in y_coords if y < 300) / len(y_coords),
                    'center_x': np.mean(x_coords),
                    'center_y': np.mean(y_coords)
                }

            # Identify interaction zones (areas with many interactive elements)
            interactive_elements = [e for e in elements if e.interactive]
            if interactive_elements:
                # Group nearby interactive elements
                zones = self._group_nearby_elements(interactive_elements, distance_threshold=100)
                layout['interaction_zones'] = [
                    {
                        'element_count': len(zone),
                        'bounds': self._get_zone_bounds(zone),
                        'types': [e.element_type.value for e in zone]
                    }
                    for zone in zones
                ]

            return layout

        except Exception as e:
            logger.error(f"❌ Error analyzing layout: {e}")
            return {}

    async def _identify_attention_areas(self, screenshot_np: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Identify areas that should draw visual attention"""
        try:
            attention_areas = []

            # Convert to grayscale for analysis
            gray = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2GRAY)

            # Find areas with high contrast (likely important)
            # Use Laplacian to detect edges/contrast
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            laplacian_abs = np.absolute(laplacian)

            # Threshold to find high-contrast areas
            threshold = np.percentile(laplacian_abs, 95)  # Top 5% contrast
            high_contrast = laplacian_abs > threshold

            # Find contours of high-contrast areas
            high_contrast_uint8 = high_contrast.astype(np.uint8) * 255
            contours, _ = cv2.findContours(high_contrast_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                if cv2.contourArea(contour) > 500:  # Filter small areas
                    x, y, w, h = cv2.boundingRect(contour)
                    attention_areas.append((x, y, w, h))

            # Also identify bright/colorful areas
            hsv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2HSV)
            saturation = hsv[:, :, 1]

            # High saturation areas (colorful)
            high_sat = saturation > np.percentile(saturation, 90)
            high_sat_uint8 = high_sat.astype(np.uint8) * 255
            contours, _ = cv2.findContours(high_sat_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                if cv2.contourArea(contour) > 300:
                    x, y, w, h = cv2.boundingRect(contour)
                    attention_areas.append((x, y, w, h))

            return attention_areas

        except Exception as e:
            logger.error(f"❌ Error identifying attention areas: {e}")
            return []

    async def _analyze_dominant_colors(self, screenshot_np: np.ndarray) -> List[Tuple[int, int, int]]:
        """Analyze dominant colors in the image"""
        try:
            # Reshape image to be a list of pixels
            pixels = screenshot_np.reshape(-1, 3)

            # Use k-means clustering to find dominant colors
            from sklearn.cluster import KMeans

            # Sample pixels for performance
            sample_size = min(10000, len(pixels))
            sampled_pixels = pixels[np.random.choice(len(pixels), sample_size, replace=False)]

            # Perform k-means clustering
            kmeans = KMeans(n_clusters=5, random_state=42, n_init=10)
            kmeans.fit(sampled_pixels)

            # Get dominant colors
            dominant_colors = kmeans.cluster_centers_.astype(int)

            return [tuple(color) for color in dominant_colors]

        except Exception as e:
            logger.error(f"❌ Error analyzing dominant colors: {e}")
            return [(128, 128, 128)]  # Default gray

    async def _learn_from_scene(self, scene: VisualScene):
        """Learn patterns from the current scene"""
        try:
            # Store scene patterns for learning
            scene_key = f"{scene.context.value}_{len(scene.elements)}"

            if scene_key not in self.visual_memory:
                self.visual_memory[scene_key] = {
                    'count': 0,
                    'element_patterns': {},
                    'layout_patterns': {}
                }

            memory = self.visual_memory[scene_key]
            memory['count'] += 1

            # Learn element patterns
            for element in scene.elements:
                elem_type = element.element_type.value
                if elem_type not in memory['element_patterns']:
                    memory['element_patterns'][elem_type] = {
                        'typical_size': [],
                        'typical_position': [],
                        'common_text': []
                    }

                patterns = memory['element_patterns'][elem_type]
                patterns['typical_size'].append(element.area())
                patterns['typical_position'].append(element.center())

                if element.text:
                    patterns['common_text'].append(element.text.lower())

            # Learn layout patterns
            memory['layout_patterns'] = scene.layout_structure

        except Exception as e:
            logger.error(f"❌ Error learning from scene: {e}")

    def _group_nearby_elements(self, elements: List[VisualElement], distance_threshold: int = 100) -> List[List[VisualElement]]:
        """Group elements that are close to each other"""
        if not elements:
            return []

        groups = []
        used = set()

        for i, elem1 in enumerate(elements):
            if i in used:
                continue

            group = [elem1]
            used.add(i)

            for j, elem2 in enumerate(elements[i+1:], i+1):
                if j in used:
                    continue

                # Calculate distance between centers
                center1 = elem1.center()
                center2 = elem2.center()
                distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)

                if distance <= distance_threshold:
                    group.append(elem2)
                    used.add(j)

            groups.append(group)

        return groups

    def _get_zone_bounds(self, elements: List[VisualElement]) -> Tuple[int, int, int, int]:
        """Get bounding box for a group of elements"""
        if not elements:
            return (0, 0, 0, 0)

        min_x = min(e.bounds[0] for e in elements)
        min_y = min(e.bounds[1] for e in elements)
        max_x = max(e.bounds[0] + e.bounds[2] for e in elements)
        max_y = max(e.bounds[1] + e.bounds[3] for e in elements)

        return (min_x, min_y, max_x - min_x, max_y - min_y)

# Global instance
visual_perception = AdvancedVisualPerception()
