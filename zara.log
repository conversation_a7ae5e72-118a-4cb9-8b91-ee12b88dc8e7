2025-08-21 16:46:05,984 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 16:46:05,985 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 16:46:07,909 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 16:46:09,526 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 16:46:09,528 - livekit.agents - INFO - starting worker
2025-08-21 16:46:09,534 - livekit.agents - INFO - initializing job runner
2025-08-21 16:46:09,534 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 16:46:09,535 - livekit.agents - INFO - job runner initialized
2025-08-21 16:46:09,574 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 505, in entrypoint
    update_zara_status("connecting", message="Connecting to LiveKit room...")
    ^^^^^^^^^^^^^^^^^^
NameError: name 'update_zara_status' is not defined
2025-08-21 16:46:19,989 - livekit.agents - INFO - shutting down worker
2025-08-21 16:46:19,990 - livekit.agents - DEBUG - shutting down job task
2025-08-21 16:46:19,992 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 16:46:19,992 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 16:46:19,993 - livekit.agents - DEBUG - job exiting
2025-08-21 16:46:22,574 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 16:46:22,574 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 16:46:24,021 - __main__ - ERROR - ZARA startup error: No module named 'zara_status_writer'
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 20, in <module>
    from zara_status_writer import (
ModuleNotFoundError: No module named 'zara_status_writer'
2025-08-21 16:47:06,910 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 16:47:06,911 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 16:47:08,790 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 16:47:10,360 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 16:47:10,362 - livekit.agents - INFO - starting worker
2025-08-21 16:47:10,367 - livekit.agents - INFO - initializing job runner
2025-08-21 16:47:10,367 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 16:47:10,368 - livekit.agents - INFO - job runner initialized
2025-08-21 16:47:13,142 - livekit.agents - WARNING - Running <Task finished name='Task-29' coro=<AgentSession._update_activity() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py:805> result=None> took too long: 2.00 seconds
2025-08-21 16:47:13,143 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 16:47:13,152 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 16:47:13,153 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 16:47:14,998 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 16:48:31,144 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 16:48:31,144 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 16:48:32,937 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 16:48:34,478 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 16:48:34,480 - livekit.agents - INFO - starting worker
2025-08-21 16:48:34,483 - livekit.agents - INFO - initializing job runner
2025-08-21 16:48:34,484 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 16:48:34,484 - livekit.agents - INFO - job runner initialized
2025-08-21 16:48:35,669 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 16:48:35,678 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 16:48:35,678 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 16:48:39,551 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 16:48:40,682 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 16:48:46,950 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 16:48:53,418 - livekit.agents - DEBUG - executing tool
2025-08-21 16:48:56,411 - livekit.agents - DEBUG - tools execution completed
2025-08-21 19:26:40,326 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 19:26:40,327 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 19:26:46,975 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 19:27:00,629 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 19:27:00,631 - livekit.agents - INFO - starting worker
2025-08-21 19:27:00,635 - livekit.agents - INFO - initializing job runner
2025-08-21 19:27:00,636 - livekit.agents - INFO - job runner initialized
2025-08-21 19:27:00,636 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 19:27:03,257 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 19:27:03,265 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 19:27:03,266 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 19:27:06,720 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 19:27:09,506 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:27:10,389 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:27:36,771 - livekit.agents - DEBUG - executing tool
2025-08-21 19:27:39,248 - livekit.agents - WARNING - Running <Task finished name='Task-120' coro=<_execute_tools_task.<locals>._traceable_fnc_tool() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py:68> result=None> took too long: 2.44 seconds
2025-08-21 19:27:39,254 - livekit.agents - DEBUG - tools execution completed
2025-08-21 19:28:44,723 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:28:50,250 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:29:33,904 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:29:43,398 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:29:52,271 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:30:16,974 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:30:31,426 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:30:43,070 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:30:59,082 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:31:06,652 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:31:09,382 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:31:24,601 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:31:28,568 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:32:18,835 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:32:35,745 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:33:05,335 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:33:06,465 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:34:00,736 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:35:04,841 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:35:21,280 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:36:06,585 - livekit.plugins.google - WARNING - Gemini server indicates disconnection soon. Time left: 50s
2025-08-21 19:36:06,586 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 19:36:07,100 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 19:36:09,350 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 19:37:11,682 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:37:13,457 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:37:22,882 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:37:26,184 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:37:59,092 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:39:11,087 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:39:40,483 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:39:44,587 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:39:56,381 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:40:50,858 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:40:54,441 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:41:44,188 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:42:05,175 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:42:33,468 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:45:09,192 - livekit.plugins.google - WARNING - Gemini server indicates disconnection soon. Time left: 50s
2025-08-21 19:45:09,193 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 19:45:09,730 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 19:45:13,109 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 19:45:43,714 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:46:58,066 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:47:49,454 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:48:29,529 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:48:41,126 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:48:57,993 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:49:02,115 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:50:02,857 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:50:17,061 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:50:48,318 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:50:59,899 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:51:37,333 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:52:10,829 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:52:16,179 - livekit.plugins.google - ERROR - error in receive task: sent 1011 (internal error) keepalive ping timeout; no close frame received
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-08-21 19:52:16,219 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 19:52:16,221 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 19:52:19,487 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 19:52:35,635 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 19:52:35,635 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 19:52:41,631 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 19:52:54,020 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 19:52:54,022 - livekit.agents - INFO - starting worker
2025-08-21 19:52:54,025 - livekit.agents - INFO - initializing job runner
2025-08-21 19:52:54,026 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 19:52:54,026 - livekit.agents - INFO - job runner initialized
2025-08-21 19:52:56,531 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 19:52:56,539 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 19:52:56,540 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 19:52:59,504 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 19:53:05,322 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:55:01,844 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:55:06,685 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:55:28,966 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:55:33,066 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:55:38,174 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:55:45,050 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:56:09,833 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:56:12,145 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:56:27,049 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:56:28,537 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:57:19,790 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:57:28,932 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:59:07,329 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:59:14,128 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:59:27,944 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 19:59:43,129 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:00:17,271 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:00:20,853 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:01:01,335 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:01:22,841 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:01:58,946 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:01:59,443 - livekit.plugins.google - WARNING - Gemini server indicates disconnection soon. Time left: 50s
2025-08-21 20:01:59,444 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:02:00,014 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:02:03,337 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:02:11,118 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:02:22,120 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:02:32,351 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:04:00,241 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:04:10,319 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:04:30,647 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:05:45,755 - livekit.plugins.google - ERROR - error in receive task: sent 1011 (internal error) keepalive ping timeout; no close frame received
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-08-21 20:05:45,758 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:05:45,759 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:05:49,134 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:06:29,660 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:06:45,985 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:08:10,872 - livekit.plugins.google - ERROR - error in send task: sent 1011 (internal error) keepalive ping timeout; no close frame received
TimeoutError: timed out while closing connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 676, in _send_task
    await session.send_realtime_input(media=media_chunk)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 333, in send_realtime_input
    await self._ws.send(json.dumps({'realtime_input': realtime_input_dict}))
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 403, in send
    async with self.send_context():
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 873, in send_context
    raise self.protocol.close_exc from original_exc
websockets.exceptions.ConnectionClosedError: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-08-21 20:08:10,874 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:08:10,875 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:08:17,769 - livekit.plugins.google - ERROR - Gemini Realtime API error: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 1236] The network connection was aborted by the local system

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:08:17,781 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 1236] The network connection was aborted by the local system

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:08:17,784 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 1236] The network connection was aborted by the local system

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:08:17,804 - livekit.agents - DEBUG - session closed
2025-08-21 20:08:25,878 - livekit.agents - INFO - shutting down worker
2025-08-21 20:08:25,879 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:08:25,881 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:08:25,881 - livekit.agents - DEBUG - job exiting
2025-08-21 20:08:25,881 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:08:28,307 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:08:28,307 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:08:30,091 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:08:31,525 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:08:31,527 - livekit.agents - INFO - starting worker
2025-08-21 20:08:31,530 - livekit.agents - INFO - initializing job runner
2025-08-21 20:08:31,531 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:08:31,531 - livekit.agents - INFO - job runner initialized
2025-08-21 20:08:32,698 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:08:32,704 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:08:32,705 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:08:36,782 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:08:37,719 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:08:39,553 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:09:14,781 - livekit.agents - INFO - shutting down worker
2025-08-21 20:09:14,782 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:09:14,783 - livekit.agents - DEBUG - job exiting
2025-08-21 20:09:14,878 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:09:15,170 - livekit.agents - WARNING - exiting forcefully
2025-08-21 20:09:18,846 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:09:18,847 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:09:20,699 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:09:22,065 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:09:22,067 - livekit.agents - INFO - starting worker
2025-08-21 20:09:22,070 - livekit.agents - INFO - initializing job runner
2025-08-21 20:09:22,070 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:09:22,071 - livekit.agents - INFO - job runner initialized
2025-08-21 20:09:23,595 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:09:23,601 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:09:23,601 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:09:28,613 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:09:29,927 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:09:37,855 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:10:21,853 - livekit.plugins.google - ERROR - error in receive task: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 580, in finish_send
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] The specified network name is no longer available

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 385, in _loop_writing
    f.result()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 584, in finish_send
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:10:21,863 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:10:21,866 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:10:24,901 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:10:33,158 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:10:42,985 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:11:10,171 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:12:46,830 - livekit.plugins.google - ERROR - error in send task: sent 1011 (internal error) keepalive ping timeout; no close frame received
TimeoutError: timed out while closing connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 676, in _send_task
    await session.send_realtime_input(media=media_chunk)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 333, in send_realtime_input
    await self._ws.send(json.dumps({'realtime_input': realtime_input_dict}))
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 403, in send
    async with self.send_context():
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 873, in send_context
    raise self.protocol.close_exc from original_exc
websockets.exceptions.ConnectionClosedError: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-08-21 20:12:46,837 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:12:46,840 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:12:46,860 - livekit.plugins.google - ERROR - Gemini Realtime API error: [Errno 11001] getaddrinfo failed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 439, in __await_impl__
    self.connection = await self.create_connection()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 365, in create_connection
    _, connection = await loop.create_connection(factory, **kwargs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1046, in create_connection
    infos = await self._ensure_resolved(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1420, in _ensure_resolved
    return await loop.getaddrinfo(host, port, family=family, type=type,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed
2025-08-21 20:12:46,898 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 439, in __await_impl__
    self.connection = await self.create_connection()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 365, in create_connection
    _, connection = await loop.create_connection(factory, **kwargs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1046, in create_connection
    infos = await self._ensure_resolved(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1420, in _ensure_resolved
    return await loop.getaddrinfo(host, port, family=family, type=type,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed
2025-08-21 20:12:46,900 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 439, in __await_impl__
    self.connection = await self.create_connection()
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 365, in create_connection
    _, connection = await loop.create_connection(factory, **kwargs)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1046, in create_connection
    infos = await self._ensure_resolved(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 1420, in _ensure_resolved
    return await loop.getaddrinfo(host, port, family=family, type=type,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\base_events.py", line 868, in getaddrinfo
    return await self.run_in_executor(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\concurrent\futures\thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\socket.py", line 962, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
socket.gaierror: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:12:46,904 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:12:46,905 - livekit.agents - DEBUG - session closed
2025-08-21 20:12:54,471 - livekit.agents - INFO - shutting down worker
2025-08-21 20:12:54,473 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:12:54,476 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:12:54,476 - livekit.agents - DEBUG - job exiting
2025-08-21 20:12:54,476 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:12:57,517 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:12:57,518 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:12:59,337 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:13:00,741 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:13:00,742 - livekit.agents - INFO - starting worker
2025-08-21 20:13:00,745 - livekit.agents - INFO - initializing job runner
2025-08-21 20:13:00,746 - livekit.agents - INFO - job runner initialized
2025-08-21 20:13:00,746 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:13:02,257 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:13:02,264 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:13:02,264 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:13:04,769 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 20:13:04,772 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 20:13:04,773 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:13:07,257 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:13:07,260 - livekit.agents - DEBUG - session closed
2025-08-21 20:13:24,213 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:15:04,389 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:15:04,390 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:15:06,191 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:15:07,627 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:15:07,629 - livekit.agents - INFO - starting worker
2025-08-21 20:15:07,633 - livekit.agents - INFO - initializing job runner
2025-08-21 20:15:07,634 - livekit.agents - INFO - job runner initialized
2025-08-21 20:15:07,634 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:15:09,208 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:15:09,215 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:15:09,215 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:15:13,713 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 20:15:13,718 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 20:15:13,719 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:15:14,229 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:15:14,232 - livekit.agents - DEBUG - session closed
2025-08-21 20:15:29,047 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:15:31,497 - livekit.agents - INFO - shutting down worker
2025-08-21 20:15:31,498 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:15:31,500 - livekit.agents - DEBUG - job exiting
2025-08-21 20:15:31,500 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:15:31,500 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:17:43,597 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:17:43,598 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:17:45,393 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:17:46,810 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:17:46,812 - livekit.agents - INFO - starting worker
2025-08-21 20:17:46,816 - livekit.agents - INFO - initializing job runner
2025-08-21 20:17:46,816 - livekit.agents - INFO - job runner initialized
2025-08-21 20:17:46,816 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:17:48,399 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:17:48,405 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:17:48,405 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:17:51,646 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 20:17:51,650 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 20:17:51,651 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:17:53,406 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:17:53,410 - livekit.agents - DEBUG - session closed
2025-08-21 20:20:16,276 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:20:16,277 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:20:18,152 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:20:19,582 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:20:19,584 - livekit.agents - INFO - starting worker
2025-08-21 20:20:19,588 - livekit.agents - INFO - initializing job runner
2025-08-21 20:20:19,588 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:20:19,588 - livekit.agents - INFO - job runner initialized
2025-08-21 20:20:21,168 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:20:21,174 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:20:21,174 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:20:26,185 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:20:31,184 - livekit.plugins.google - ERROR - Gemini Realtime API error: timed out during handshake
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 20:20:31,188 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 20:20:31,188 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:20:31,190 - livekit.agents - DEBUG - session closed
2025-08-21 20:20:42,399 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:21:27,243 - livekit.agents - INFO - shutting down worker
2025-08-21 20:21:27,245 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:21:27,247 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:21:27,247 - livekit.agents - DEBUG - job exiting
2025-08-21 20:21:27,247 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:21:30,653 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:21:30,654 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:21:32,439 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:21:33,840 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:21:33,842 - livekit.agents - INFO - starting worker
2025-08-21 20:21:33,845 - livekit.agents - INFO - initializing job runner
2025-08-21 20:21:33,846 - livekit.agents - INFO - job runner initialized
2025-08-21 20:21:33,846 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:21:35,396 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:21:35,401 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:21:35,402 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:21:38,921 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:22:37,423 - livekit.agents - DEBUG - executing tool
2025-08-21 20:22:37,520 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:23:00,500 - livekit.agents - DEBUG - executing tool
2025-08-21 20:23:01,509 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:23:15,140 - livekit.agents - DEBUG - executing tool
2025-08-21 20:23:16,148 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:23:46,564 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:29:17,751 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:29:17,751 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:29:19,624 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:29:21,335 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:29:21,336 - livekit.agents - INFO - starting worker
2025-08-21 20:29:21,340 - livekit.agents - INFO - initializing job runner
2025-08-21 20:29:21,341 - livekit.agents - INFO - job runner initialized
2025-08-21 20:29:21,341 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:29:22,968 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:29:22,977 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:29:22,977 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:29:26,415 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:30:54,465 - livekit.agents - INFO - shutting down worker
2025-08-21 20:30:54,466 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:30:54,469 - livekit.agents - DEBUG - job exiting
2025-08-21 20:30:54,632 - livekit.agents - WARNING - exiting forcefully
2025-08-21 20:30:57,091 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:30:57,092 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:30:58,963 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:30:58,965 - __main__ - ERROR - ZARA startup error: no running event loop
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 76, in <module>
    HTTP_CONNECTOR = aiohttp.TCPConnector(
                     ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 963, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 313, in __init__
    loop = loop or asyncio.get_running_loop()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: no running event loop
2025-08-21 20:32:31,743 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:32:31,743 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:32:33,481 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:32:33,483 - __main__ - ERROR - ZARA startup error: no running event loop
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 76, in <module>
    HTTP_CONNECTOR = aiohttp.TCPConnector(
                     ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 963, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 313, in __init__
    loop = loop or asyncio.get_running_loop()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: no running event loop
2025-08-21 20:33:00,376 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:33:00,377 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:33:02,205 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:33:02,207 - __main__ - ERROR - ZARA startup error: no running event loop
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 76, in <module>
    HTTP_CONNECTOR = aiohttp.TCPConnector(
                     ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 963, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 313, in __init__
    loop = loop or asyncio.get_running_loop()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: no running event loop
2025-08-21 20:33:34,979 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:33:34,980 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:33:36,840 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:33:36,842 - __main__ - ERROR - ZARA startup error: no running event loop
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 76, in <module>
    HTTP_CONNECTOR = aiohttp.TCPConnector(
                     ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 963, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 313, in __init__
    loop = loop or asyncio.get_running_loop()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: no running event loop
2025-08-21 20:36:47,599 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:36:47,599 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:37:19,895 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:37:19,895 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:37:21,383 - __main__ - ERROR - ZARA startup error: unexpected indent (tools.py, line 760)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 760
    try:
IndentationError: unexpected indent
2025-08-21 20:37:38,733 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:37:38,734 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:37:40,350 - __main__ - ERROR - ZARA startup error: unindent does not match any outer indentation level (tools.py, line 927)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 927
    except Exception as e:
                          ^
IndentationError: unindent does not match any outer indentation level
2025-08-21 20:38:52,806 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:38:52,806 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:41:05,040 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:41:05,041 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:42:14,832 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:42:14,832 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:42:16,595 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:42:16,597 - __main__ - ERROR - ZARA startup error: no running event loop
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 77, in <module>
    HTTP_CONNECTOR = aiohttp.TCPConnector(
                     ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 963, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 313, in __init__
    loop = loop or asyncio.get_running_loop()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: no running event loop
2025-08-21 20:42:42,793 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:42:42,793 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:42:44,580 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:42:44,581 - __main__ - ERROR - ZARA startup error: no running event loop
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 34, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 77, in <module>
    HTTP_CONNECTOR = aiohttp.TCPConnector(
                     ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 963, in __init__
    super().__init__(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\aiohttp\connector.py", line 313, in __init__
    loop = loop or asyncio.get_running_loop()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: no running event loop
2025-08-21 20:43:23,346 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:43:23,346 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:43:25,167 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:43:26,663 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:43:26,665 - livekit.agents - INFO - starting worker
2025-08-21 20:43:26,669 - livekit.agents - INFO - initializing job runner
2025-08-21 20:43:26,670 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:43:26,670 - livekit.agents - INFO - job runner initialized
2025-08-21 20:43:28,288 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:43:28,293 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:43:28,294 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:43:33,306 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:43:35,805 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:43:49,169 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:44:02,191 - livekit.plugins.google - ERROR - error in receive task: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 580, in finish_send
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] The specified network name is no longer available

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 385, in _loop_writing
    f.result()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 584, in finish_send
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:44:02,204 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:44:02,211 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:44:05,303 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:44:39,557 - livekit.agents - DEBUG - executing tool
2025-08-21 20:44:42,592 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:47:42,613 - livekit.agents - INFO - shutting down worker
2025-08-21 20:47:42,614 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:47:42,616 - livekit.agents - DEBUG - job exiting
2025-08-21 20:47:42,618 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:47:43,059 - livekit.agents - DEBUG - session closed
2025-08-21 20:47:43,059 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:47:43,060 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:47:46,729 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:47:46,729 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:47:48,424 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:47:49,857 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:47:49,859 - livekit.agents - INFO - starting worker
2025-08-21 20:47:49,862 - livekit.agents - INFO - initializing job runner
2025-08-21 20:47:49,862 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:47:49,863 - livekit.agents - INFO - job runner initialized
2025-08-21 20:47:51,377 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:47:51,382 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:47:51,382 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:47:54,837 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001; then sent 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001; then sent 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001
2025-08-21 20:47:54,840 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001; then sent 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001
2025-08-21 20:47:54,841 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001; then sent 1007 (invalid frame payload data) Requested voice api_name 'luna' is not available for model models/gemini-2.0-flash-live-001

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:47:56,396 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:47:56,399 - livekit.agents - DEBUG - session closed
2025-08-21 20:48:08,825 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:50:32,398 - livekit.agents - INFO - shutting down worker
2025-08-21 20:50:32,399 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:50:32,400 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:50:32,400 - livekit.agents - DEBUG - job exiting
2025-08-21 20:50:32,401 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:50:38,903 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:50:38,904 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:50:40,879 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:50:42,475 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:50:42,477 - livekit.agents - INFO - starting worker
2025-08-21 20:50:42,481 - livekit.agents - INFO - initializing job runner
2025-08-21 20:50:42,481 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:50:42,481 - livekit.agents - INFO - job runner initialized
2025-08-21 20:50:43,739 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:50:43,746 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:50:43,746 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:50:45,427 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:50:47,901 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:51:37,998 - livekit.agents - DEBUG - executing tool
2025-08-21 20:51:41,034 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:51:45,963 - livekit.agents - DEBUG - executing tool
2025-08-21 20:51:46,027 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:52:14,115 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:52:36,713 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:52:42,013 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:52:46,399 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:54:37,861 - livekit.plugins.google - ERROR - error in send task: sent 1011 (internal error) keepalive ping timeout; no close frame received
TimeoutError: timed out while closing connection

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 676, in _send_task
    await session.send_realtime_input(media=media_chunk)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 333, in send_realtime_input
    await self._ws.send(json.dumps({'realtime_input': realtime_input_dict}))
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 403, in send
    async with self.send_context():
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 873, in send_context
    raise self.protocol.close_exc from original_exc
websockets.exceptions.ConnectionClosedError: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-08-21 20:54:37,866 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:54:37,867 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:54:50,703 - livekit.plugins.google - ERROR - Gemini Realtime API error: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 1236] The network connection was aborted by the local system

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:54:50,710 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 1236] The network connection was aborted by the local system

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:54:50,713 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 1236] The network connection was aborted by the local system

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:54:50,718 - livekit.agents - DEBUG - session closed
2025-08-21 20:54:52,953 - livekit.agents - INFO - shutting down worker
2025-08-21 20:54:52,953 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:54:52,954 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:54:52,956 - livekit.agents - DEBUG - job exiting
2025-08-21 20:54:52,956 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:54:55,144 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:54:55,144 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:54:56,865 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:54:58,252 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:54:58,254 - livekit.agents - INFO - starting worker
2025-08-21 20:54:58,257 - livekit.agents - INFO - initializing job runner
2025-08-21 20:54:58,258 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:54:58,258 - livekit.agents - INFO - job runner initialized
2025-08-21 20:54:59,413 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:54:59,419 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:54:59,419 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:55:00,900 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:55:04,424 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:55:27,184 - livekit.plugins.google - ERROR - error in receive task: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 580, in finish_send
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] The specified network name is no longer available

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 385, in _loop_writing
    f.result()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 584, in finish_send
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:55:27,194 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:55:27,202 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:55:27,218 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:55:38,385 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:56:03,803 - livekit.plugins.google - ERROR - error in receive task: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 580, in finish_send
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] The specified network name is no longer available

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 385, in _loop_writing
    f.result()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 584, in finish_send
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:56:03,811 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:56:03,815 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:56:08,237 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:56:36,969 - livekit.agents - DEBUG - executing tool
2025-08-21 20:56:36,975 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:56:52,043 - livekit.plugins.google - ERROR - error in receive task: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 580, in finish_send
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] The specified network name is no longer available

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 385, in _loop_writing
    f.result()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 584, in finish_send
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 20:56:52,047 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 20:56:52,055 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:57:02,059 - livekit.plugins.google - ERROR - Gemini Realtime API error: timed out during handshake
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 20:57:02,061 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 20:57:02,062 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 20:57:02,064 - livekit.agents - DEBUG - session closed
2025-08-21 20:57:04,552 - livekit.agents - INFO - shutting down worker
2025-08-21 20:57:04,553 - livekit.agents - DEBUG - shutting down job task
2025-08-21 20:57:04,555 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 20:57:04,555 - livekit.agents - DEBUG - job exiting
2025-08-21 20:57:04,555 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 20:57:09,597 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 20:57:09,597 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 20:57:11,263 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 20:57:12,640 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:57:12,642 - livekit.agents - INFO - starting worker
2025-08-21 20:57:12,645 - livekit.agents - INFO - initializing job runner
2025-08-21 20:57:12,645 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 20:57:12,646 - livekit.agents - INFO - job runner initialized
2025-08-21 20:57:14,208 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 20:57:14,214 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:57:14,214 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 20:57:17,433 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 20:57:19,218 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:57:32,226 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 20:58:03,526 - livekit.agents - DEBUG - executing tool
2025-08-21 20:58:04,535 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:58:04,539 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:58:12,546 - livekit.plugins.google - WARNING - server cancelled tool calls
2025-08-21 20:58:12,557 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:58:13,329 - livekit.agents - DEBUG - executing tool
2025-08-21 20:58:14,337 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:58:40,067 - livekit.agents - DEBUG - executing tool
2025-08-21 20:58:41,074 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:58:57,054 - livekit.agents - DEBUG - executing tool
2025-08-21 20:58:57,055 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:59:13,302 - livekit.agents - DEBUG - executing tool
2025-08-21 20:59:13,303 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:59:34,362 - livekit.agents - DEBUG - executing tool
2025-08-21 20:59:37,595 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:59:52,733 - livekit.agents - DEBUG - executing tool
2025-08-21 20:59:53,137 - livekit.agents - DEBUG - tools execution completed
2025-08-21 20:59:58,125 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 20:59:59,383 - livekit.agents - DEBUG - executing tool
2025-08-21 20:59:59,485 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:00:13,544 - livekit.agents - DEBUG - executing tool
2025-08-21 21:00:13,646 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:00:17,205 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:00:18,554 - livekit.agents - DEBUG - executing tool
2025-08-21 21:00:18,656 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:00:28,403 - livekit.agents - DEBUG - executing tool
2025-08-21 21:00:31,021 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:00:32,548 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:00:33,672 - livekit.agents - DEBUG - executing tool
2025-08-21 21:00:33,774 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:00:38,023 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:00:39,454 - livekit.agents - DEBUG - executing tool
2025-08-21 21:00:39,466 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:00:46,931 - livekit.agents - DEBUG - executing tool
2025-08-21 21:00:50,131 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:00:55,061 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:00:58,189 - livekit.agents - DEBUG - executing tool
2025-08-21 21:00:58,594 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:01:05,909 - livekit.agents - DEBUG - executing tool
2025-08-21 21:01:06,012 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:01:07,997 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:01:09,095 - livekit.agents - DEBUG - executing tool
2025-08-21 21:01:09,199 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:01:20,029 - livekit.agents - DEBUG - executing tool
2025-08-21 21:01:26,862 - livekit.plugins.google - WARNING - server cancelled tool calls
2025-08-21 21:01:28,408 - livekit.agents - DEBUG - executing tool
2025-08-21 21:01:33,891 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:02:07,225 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:02:07,226 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:02:08,991 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:02:10,455 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:02:10,457 - livekit.agents - INFO - starting worker
2025-08-21 21:02:10,461 - livekit.agents - INFO - initializing job runner
2025-08-21 21:02:10,461 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:02:10,461 - livekit.agents - INFO - job runner initialized
2025-08-21 21:02:12,021 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 21:02:12,028 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:02:12,029 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:02:14,233 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 21:02:19,541 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:02:42,920 - livekit.agents - DEBUG - executing tool
2025-08-21 21:02:46,151 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:02:59,708 - livekit.agents - DEBUG - executing tool
2025-08-21 21:03:00,716 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:03:31,425 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:04:03,888 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:04:03,888 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:04:06,304 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:04:13,046 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:04:13,186 - livekit.agents - INFO - starting worker
2025-08-21 21:04:13,301 - livekit.agents - INFO - initializing job runner
2025-08-21 21:04:13,379 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:04:13,380 - livekit.agents - INFO - job runner initialized
2025-08-21 21:04:17,187 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 21:04:17,195 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:04:17,195 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:04:19,865 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 21:04:25,939 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:04:36,010 - livekit.agents - DEBUG - executing tool
2025-08-21 21:04:39,217 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:04:49,961 - livekit.agents - DEBUG - executing tool
2025-08-21 21:04:50,567 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:06:11,060 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:06:34,583 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:06:36,617 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 21:10:30,498 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:10:30,499 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:10:32,417 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:10:33,956 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:10:33,958 - livekit.agents - INFO - starting worker
2025-08-21 21:10:33,961 - livekit.agents - INFO - initializing job runner
2025-08-21 21:10:33,962 - livekit.agents - INFO - job runner initialized
2025-08-21 21:10:33,962 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:10:38,031 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 537, in entrypoint
    timeout=timeout
    ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 227, in __init__
    self._tool_patterns = self._initialize_tool_patterns()
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Assistant' object has no attribute '_initialize_tool_patterns'
2025-08-21 21:10:39,244 - livekit.agents - INFO - shutting down worker
2025-08-21 21:10:39,245 - livekit.agents - DEBUG - shutting down job task
2025-08-21 21:10:39,246 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 21:10:39,246 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 21:10:39,247 - livekit.agents - DEBUG - job exiting
2025-08-21 21:27:59,101 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:27:59,102 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:28:06,837 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:28:22,194 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:28:22,197 - livekit.agents - INFO - starting worker
2025-08-21 21:28:22,201 - livekit.agents - INFO - initializing job runner
2025-08-21 21:28:22,202 - livekit.agents - INFO - job runner initialized
2025-08-21 21:28:22,202 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:28:24,959 - livekit.agents - WARNING - Running <Task finished name='Task-29' coro=<AgentSession._update_activity() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py:805> result=None> took too long: 2.00 seconds
2025-08-21 21:28:24,960 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 21:28:24,967 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:28:24,968 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:28:30,004 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 21:28:34,965 - livekit.plugins.google - ERROR - Gemini Realtime API error: timed out during handshake
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 21:28:35,002 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 21:28:35,003 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 21:28:35,005 - livekit.agents - DEBUG - session closed
2025-08-21 21:28:40,976 - livekit.agents - INFO - shutting down worker
2025-08-21 21:28:40,977 - livekit.agents - DEBUG - shutting down job task
2025-08-21 21:28:40,977 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 21:28:40,978 - livekit.agents - DEBUG - job exiting
2025-08-21 21:28:40,978 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 21:28:41,179 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 21:28:55,891 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:28:55,891 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:28:57,778 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:28:59,298 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:28:59,299 - livekit.agents - INFO - starting worker
2025-08-21 21:28:59,302 - livekit.agents - INFO - initializing job runner
2025-08-21 21:28:59,303 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:28:59,303 - livekit.agents - INFO - job runner initialized
2025-08-21 21:29:03,358 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 856, in entrypoint
    agent = Assistant()
            ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 199, in __init__
    instructions=self._build_instructions(),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 590, in _build_instructions
    - Ultra-Fast Execution: {'Enabled' if self._auto_execution_enabled else 'Disabled'}
                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Assistant' object has no attribute '_auto_execution_enabled'
2025-08-21 21:29:09,485 - livekit.agents - INFO - shutting down worker
2025-08-21 21:29:09,486 - livekit.agents - DEBUG - shutting down job task
2025-08-21 21:29:09,488 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 21:29:09,488 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 21:29:09,489 - livekit.agents - DEBUG - job exiting
2025-08-21 21:30:15,584 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:30:15,585 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:30:17,335 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:30:18,806 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:30:18,807 - livekit.agents - INFO - starting worker
2025-08-21 21:30:18,810 - livekit.agents - INFO - initializing job runner
2025-08-21 21:30:18,811 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:30:18,811 - livekit.agents - INFO - job runner initialized
2025-08-21 21:30:22,857 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 856, in entrypoint
    agent = Assistant()
            ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 199, in __init__
    instructions=self._build_instructions(),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 590, in _build_instructions
    - Ultra-Fast Execution: {'Enabled' if self._auto_execution_enabled else 'Disabled'}
                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Assistant' object has no attribute '_auto_execution_enabled'
2025-08-21 21:30:49,630 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:30:49,630 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:30:51,428 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:31:30,076 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:31:30,076 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:31:31,834 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:31:33,346 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:31:33,348 - livekit.agents - INFO - starting worker
2025-08-21 21:31:33,351 - livekit.agents - INFO - initializing job runner
2025-08-21 21:31:33,352 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:31:33,352 - livekit.agents - INFO - job runner initialized
2025-08-21 21:31:37,417 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 856, in entrypoint
    agent = Assistant()
            ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 199, in __init__
    instructions=self._build_instructions(),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 590, in _build_instructions
    - Ultra-Fast Execution: {'Enabled' if self._auto_execution_enabled else 'Disabled'}
                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Assistant' object has no attribute '_auto_execution_enabled'
2025-08-21 21:31:52,488 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:31:52,489 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:31:54,236 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:31:55,667 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:31:55,669 - livekit.agents - INFO - starting worker
2025-08-21 21:31:55,671 - livekit.agents - INFO - initializing job runner
2025-08-21 21:31:55,672 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:31:55,672 - livekit.agents - INFO - job runner initialized
2025-08-21 21:31:59,726 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 856, in entrypoint
    agent = Assistant()
            ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 199, in __init__
    instructions=self._build_instructions(),
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 590, in _build_instructions
    - Ultra-Fast Execution: {'Enabled' if self._auto_execution_enabled else 'Disabled'}
                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Assistant' object has no attribute '_auto_execution_enabled'
2025-08-21 21:32:14,848 - livekit.agents - INFO - shutting down worker
2025-08-21 21:32:14,850 - livekit.agents - DEBUG - shutting down job task
2025-08-21 21:32:14,851 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 21:32:14,851 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 21:32:14,852 - livekit.agents - DEBUG - job exiting
2025-08-21 21:33:45,780 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:33:45,781 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:33:47,647 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:33:49,162 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:33:49,163 - livekit.agents - INFO - starting worker
2025-08-21 21:33:49,166 - livekit.agents - INFO - initializing job runner
2025-08-21 21:33:49,167 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:33:49,167 - livekit.agents - INFO - job runner initialized
2025-08-21 21:33:50,780 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 21:33:50,787 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:33:50,787 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:33:54,058 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 21:34:31,623 - livekit.agents - DEBUG - executing tool
2025-08-21 21:34:31,721 - livekit.agents - DEBUG - tools execution completed
2025-08-21 21:59:11,446 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 21:59:11,447 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 21:59:18,836 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 21:59:41,133 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:59:41,135 - livekit.agents - INFO - starting worker
2025-08-21 21:59:41,139 - livekit.agents - INFO - initializing job runner
2025-08-21 21:59:41,139 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 21:59:41,139 - livekit.agents - INFO - job runner initialized
2025-08-21 21:59:43,617 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 21:59:43,623 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:59:43,624 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 21:59:46,383 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 21:59:48,639 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 21:59:59,456 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 22:00:23,693 - livekit.plugins.google - ERROR - error in receive task: no close frame received or sent
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 286, in _loop_reading
    length = fut.result()
             ^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 494, in finish_recv
    return ov.getresult()
           ^^^^^^^^^^^^^^
ConnectionAbortedError: [WinError 1236] The network connection was aborted by the local system

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-08-21 22:00:23,739 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 22:00:23,741 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:00:26,854 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:00:48,881 - livekit.agents - DEBUG - executing tool
2025-08-21 22:00:48,982 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:01:02,060 - livekit.agents - DEBUG - executing tool
2025-08-21 22:01:02,061 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:02:32,606 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:03:13,290 - livekit.agents - DEBUG - executing tool
2025-08-21 22:03:16,517 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:03:40,476 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:03:54,579 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:05:10,904 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:05:12,076 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:05:13,914 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:05:16,301 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:06:44,296 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:06:44,296 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:06:49,709 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:07:02,144 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:07:02,145 - livekit.agents - INFO - starting worker
2025-08-21 22:07:02,148 - livekit.agents - INFO - initializing job runner
2025-08-21 22:07:02,148 - livekit.agents - INFO - job runner initialized
2025-08-21 22:07:02,148 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:07:04,439 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:07:04,446 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:07:04,446 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:07:07,672 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:07:09,887 - livekit.agents - INFO - shutting down worker
2025-08-21 22:07:09,894 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:07:09,895 - livekit.agents - DEBUG - job exiting
2025-08-21 22:07:09,898 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:07:09,902 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 22:07:09,994 - livekit.agents - WARNING - exiting forcefully
2025-08-21 22:07:37,518 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:07:37,518 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:07:39,217 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:07:40,617 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:07:40,618 - livekit.agents - INFO - starting worker
2025-08-21 22:07:40,621 - livekit.agents - INFO - initializing job runner
2025-08-21 22:07:40,622 - livekit.agents - INFO - job runner initialized
2025-08-21 22:07:40,622 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:07:42,200 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:07:42,206 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:07:42,206 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:07:45,726 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:07:48,460 - livekit.agents - INFO - shutting down worker
2025-08-21 22:07:48,461 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:07:48,463 - livekit.agents - DEBUG - job exiting
2025-08-21 22:07:48,466 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:07:48,473 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 22:07:48,745 - livekit.agents - WARNING - exiting forcefully
2025-08-21 22:08:21,213 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:08:21,213 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:08:22,936 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:08:24,410 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:08:24,412 - livekit.agents - INFO - starting worker
2025-08-21 22:08:24,415 - livekit.agents - INFO - initializing job runner
2025-08-21 22:08:24,416 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:08:24,416 - livekit.agents - INFO - job runner initialized
2025-08-21 22:08:26,042 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:08:26,047 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:08:26,049 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:08:29,453 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:08:30,255 - livekit.agents - INFO - shutting down worker
2025-08-21 22:08:30,256 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:08:30,257 - livekit.agents - DEBUG - job exiting
2025-08-21 22:08:30,408 - livekit.agents - WARNING - exiting forcefully
2025-08-21 22:09:33,386 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:09:33,386 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:09:35,127 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:09:36,592 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:09:36,594 - livekit.agents - INFO - starting worker
2025-08-21 22:09:36,597 - livekit.agents - INFO - initializing job runner
2025-08-21 22:09:36,598 - livekit.agents - INFO - job runner initialized
2025-08-21 22:09:36,598 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:09:38,212 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:09:38,218 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:09:38,219 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:09:41,301 - livekit.agents - INFO - shutting down worker
2025-08-21 22:09:41,302 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:09:41,303 - livekit.agents - DEBUG - job exiting
2025-08-21 22:09:41,445 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:09:42,447 - livekit.agents - WARNING - exiting forcefully
2025-08-21 22:10:17,862 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:10:17,862 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:10:19,591 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:10:21,046 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:10:21,048 - livekit.agents - INFO - starting worker
2025-08-21 22:10:21,051 - livekit.agents - INFO - initializing job runner
2025-08-21 22:10:21,052 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:10:21,052 - livekit.agents - INFO - job runner initialized
2025-08-21 22:10:21,323 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:10:32,681 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:10:32,682 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:10:34,426 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:10:35,875 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:10:35,877 - livekit.agents - INFO - starting worker
2025-08-21 22:10:35,880 - livekit.agents - INFO - initializing job runner
2025-08-21 22:10:35,881 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:10:35,881 - livekit.agents - INFO - job runner initialized
2025-08-21 22:10:36,161 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:10:37,577 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:10:37,585 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:10:37,585 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:10:37,907 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:10:40,691 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:11:43,771 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:11:43,772 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:11:45,401 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:11:46,755 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:11:46,756 - livekit.agents - INFO - starting worker
2025-08-21 22:11:46,759 - livekit.agents - INFO - initializing job runner
2025-08-21 22:11:46,760 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:11:46,760 - livekit.agents - INFO - job runner initialized
2025-08-21 22:11:46,978 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:11:48,379 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:11:48,385 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:11:48,386 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:11:49,282 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:11:51,463 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:11:52,189 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:11:55,112 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:11:58,160 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:12:01,151 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:12:03,916 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:12:06,726 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:12:09,912 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:12:13,093 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:12:16,363 - tools - ERROR - UI element detection error: UIElement.__init__() got an unexpected keyword argument 'is_active'
2025-08-21 22:12:20,802 - livekit.agents - INFO - shutting down worker
2025-08-21 22:12:20,803 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:12:20,805 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 22:12:20,805 - livekit.agents - DEBUG - job exiting
2025-08-21 22:12:21,156 - livekit.agents - DEBUG - session closed
2025-08-21 22:12:21,156 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 22:12:21,156 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 22:14:12,350 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:14:12,351 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:14:14,157 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:14:15,574 - __main__ - ERROR - ZARA startup error: name 'ScreenContext' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 39, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 5444, in <module>
    class ContinuousScreenMonitor:
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 5495, in ContinuousScreenMonitor
    def _capture_and_analyze(self) -> Optional[ScreenContext]:
                                               ^^^^^^^^^^^^^
NameError: name 'ScreenContext' is not defined
2025-08-21 22:14:48,640 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:14:48,640 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:14:50,430 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:14:51,858 - __main__ - ERROR - ZARA startup error: name 'ScreenContext' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 39, in <module>
    from tools import (
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 5439, in <module>
    class ContinuousScreenMonitor:
  File "C:\Users\<USER>\Desktop\ZARA\tools.py", line 5490, in ContinuousScreenMonitor
    def _capture_and_analyze(self) -> Optional[ScreenContext]:
                                               ^^^^^^^^^^^^^
NameError: name 'ScreenContext' is not defined
2025-08-21 22:18:14,542 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:18:14,542 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:18:16,312 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:18:17,759 - __main__ - ERROR - ZARA startup error: cannot import name 'start_continuous_screen_monitoring' from 'tools' (C:\Users\<USER>\Desktop\ZARA\tools.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 39, in <module>
    from tools import (
ImportError: cannot import name 'start_continuous_screen_monitoring' from 'tools' (C:\Users\<USER>\Desktop\ZARA\tools.py)
2025-08-21 22:18:37,578 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:18:37,578 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:18:39,299 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:18:40,695 - __main__ - ERROR - ZARA startup error: cannot import name 'start_continuous_screen_monitoring' from 'tools' (C:\Users\<USER>\Desktop\ZARA\tools.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 39, in <module>
    from tools import (
ImportError: cannot import name 'start_continuous_screen_monitoring' from 'tools' (C:\Users\<USER>\Desktop\ZARA\tools.py)
2025-08-21 22:21:35,236 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:21:35,236 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:21:37,000 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:21:38,414 - __main__ - ERROR - ZARA startup error: cannot import name 'analyze_user_emotion_and_respond' from 'tools' (C:\Users\<USER>\Desktop\ZARA\tools.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 39, in <module>
    from tools import (
ImportError: cannot import name 'analyze_user_emotion_and_respond' from 'tools' (C:\Users\<USER>\Desktop\ZARA\tools.py)
2025-08-21 22:23:12,263 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:23:12,263 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:23:14,037 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:23:15,444 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:23:15,446 - livekit.agents - INFO - starting worker
2025-08-21 22:23:15,448 - livekit.agents - INFO - initializing job runner
2025-08-21 22:23:15,449 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:23:15,449 - livekit.agents - INFO - job runner initialized
2025-08-21 22:23:17,162 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:23:17,168 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:23:17,168 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:23:20,435 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:23:32,483 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:25:27,301 - livekit.agents - DEBUG - executing tool
2025-08-21 22:25:30,504 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:25:39,317 - livekit.agents - DEBUG - executing tool
2025-08-21 22:25:39,723 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:25:49,936 - livekit.agents - DEBUG - executing tool
2025-08-21 22:25:50,038 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:25:51,683 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:29:10,733 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:29:10,734 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:29:12,463 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:29:13,909 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:29:13,911 - livekit.agents - INFO - starting worker
2025-08-21 22:29:13,913 - livekit.agents - INFO - initializing job runner
2025-08-21 22:29:13,914 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:29:13,914 - livekit.agents - INFO - job runner initialized
2025-08-21 22:29:15,762 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:29:15,769 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:29:15,769 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:29:19,088 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:29:54,685 - livekit.agents - DEBUG - executing tool
2025-08-21 22:29:54,686 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:30:10,642 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:30:26,198 - livekit.agents - DEBUG - executing tool
2025-08-21 22:30:26,875 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:30:54,618 - livekit.agents - DEBUG - executing tool
2025-08-21 22:30:54,822 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:31:02,037 - livekit.agents - DEBUG - executing tool
2025-08-21 22:31:02,075 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:31:03,032 - livekit.agents - DEBUG - executing tool
2025-08-21 22:31:03,059 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:31:57,772 - livekit.agents - DEBUG - executing tool
2025-08-21 22:31:57,975 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:31:59,572 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:32:27,916 - livekit.agents - DEBUG - executing tool
2025-08-21 22:32:28,120 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:32:30,200 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:32:31,920 - livekit.agents - DEBUG - executing tool
2025-08-21 22:32:35,630 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:32:42,981 - livekit.agents - DEBUG - executing tool
2025-08-21 22:32:43,032 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:32:48,967 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:33:22,656 - livekit.agents - DEBUG - executing tool
2025-08-21 22:33:23,608 - tools - DEBUG - DuckDuckGo API search failed: 200, message='Attempt to decode JSON with unexpected mimetype: application/x-javascript', url='https://api.duckduckgo.com/?q=cursor&format=json&no_redirect=1&no_html=1&skip_disambig=1'
2025-08-21 22:33:24,762 - tools - DEBUG - Wikipedia search failed: Page id "coursor" does not match any pages. Try another id!
2025-08-21 22:33:24,763 - tools - WARNING - Slow call detected: search_web took 2.10s
2025-08-21 22:33:24,763 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:34:12,663 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:34:12,664 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:34:14,555 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:34:16,063 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:34:16,065 - livekit.agents - INFO - starting worker
2025-08-21 22:34:16,069 - livekit.agents - INFO - initializing job runner
2025-08-21 22:34:16,069 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:34:16,069 - livekit.agents - INFO - job runner initialized
2025-08-21 22:34:18,041 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:34:21,077 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:34:22,281 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:34:24,088 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:34:24,097 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 908, in entrypoint
    await asyncio.wait_for(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\tasks.py", line 489, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py", line 502, in start
    await asyncio.gather(*tasks)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py", line 848, in _update_activity
    await self._activity.start()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 378, in start
    await self._start_session()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 453, in _start_session
    await self._rt_session.update_tools(self.tools)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 437, in update_tools
    self._tools = llm.ToolContext(tools)
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\llm\tool_context.py", line 247, in update_tools
    raise ValueError(f"duplicate function name: {info.name}")
ValueError: duplicate function name: click_on_text
2025-08-21 22:34:25,142 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:34:26,205 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:35:32,088 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:35:32,089 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:35:33,769 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:35:35,289 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:35:35,291 - livekit.agents - INFO - starting worker
2025-08-21 22:35:35,295 - livekit.agents - INFO - initializing job runner
2025-08-21 22:35:35,296 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:35:35,296 - livekit.agents - INFO - job runner initialized
2025-08-21 22:35:37,080 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:35:38,852 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:35:40,094 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:35:43,018 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:35:43,026 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 908, in entrypoint
    await asyncio.wait_for(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\tasks.py", line 489, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py", line 502, in start
    await asyncio.gather(*tasks)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py", line 848, in _update_activity
    await self._activity.start()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 378, in start
    await self._start_session()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 453, in _start_session
    await self._rt_session.update_tools(self.tools)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 437, in update_tools
    self._tools = llm.ToolContext(tools)
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\llm\tool_context.py", line 247, in update_tools
    raise ValueError(f"duplicate function name: {info.name}")
ValueError: duplicate function name: click_on_text
2025-08-21 22:35:43,415 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:35:43,418 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:35:43,420 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 22:35:45,048 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:35:45,050 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:35:45,051 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 22:36:11,554 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:36:11,555 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:36:13,304 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:36:14,831 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:36:14,833 - livekit.agents - INFO - starting worker
2025-08-21 22:36:14,837 - livekit.agents - INFO - initializing job runner
2025-08-21 22:36:14,837 - livekit.agents - INFO - job runner initialized
2025-08-21 22:36:14,837 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:36:16,708 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:37:39,609 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:37:39,609 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:37:41,334 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:37:42,792 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:37:42,794 - livekit.agents - INFO - starting worker
2025-08-21 22:37:42,797 - livekit.agents - INFO - initializing job runner
2025-08-21 22:37:42,798 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:37:42,798 - livekit.agents - INFO - job runner initialized
2025-08-21 22:37:44,609 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:37:47,639 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:37:48,030 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:37:48,034 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:37:48,035 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 22:37:49,590 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:37:49,590 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:37:49,592 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 22:37:50,593 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:37:50,604 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\ZARA\Zara_Voice_Assistant.py", line 908, in entrypoint
    await asyncio.wait_for(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\tasks.py", line 489, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py", line 502, in start
    await asyncio.gather(*tasks)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py", line 848, in _update_activity
    await self._activity.start()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 378, in start
    await self._start_session()
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 453, in _start_session
    await self._rt_session.update_tools(self.tools)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 437, in update_tools
    self._tools = llm.ToolContext(tools)
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\llm\tool_context.py", line 247, in update_tools
    raise ValueError(f"duplicate function name: {info.name}")
ValueError: duplicate function name: click_on_text
2025-08-21 22:37:52,899 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:37:52,899 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:37:52,902 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 22:37:58,759 - livekit.agents - INFO - shutting down worker
2025-08-21 22:37:58,760 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:37:58,762 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 22:37:58,762 - livekit.agents - DEBUG - job exiting
2025-08-21 22:37:58,762 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 22:37:58,765 - tools - ERROR - Text extraction error: (3221225786, 'Estimating resolution as 165 ObjectCache(00007ffdd1596600)::~ObjectCache(): WARNING! LEAK! object 000002718fc93af0 still has count 1 (id C:\\Program Files\\Tesseract-OCR/tessdata/eng.traineddatalstm-punc-dawg) ObjectCache(00007ffdd1596600)::~ObjectCache(): WARNING! LEAK! object 000002718fd483b0 still has count 1 (id C:\\Program Files\\Tesseract-OCR/tessdata/eng.traineddatalstm-word-dawg) ObjectCache(00007ffdd1596600)::~ObjectCache(): WARNING! LEAK! object 000002718f3cbc70 still has count 1 (id C:\\Program Files\\Tesseract-OCR/tessdata/eng.traineddatalstm-number-dawg)')
2025-08-21 22:38:39,396 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:38:39,396 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:38:41,131 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:38:42,567 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:38:42,569 - livekit.agents - INFO - starting worker
2025-08-21 22:38:42,573 - livekit.agents - INFO - initializing job runner
2025-08-21 22:38:42,574 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:38:42,574 - livekit.agents - INFO - job runner initialized
2025-08-21 22:38:44,367 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:38:44,375 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:38:44,375 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:38:47,228 - livekit.plugins.google - ERROR - Gemini Realtime API error: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:38:47,231 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h
2025-08-21 22:38:47,232 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1057, in connect
    logger.info(await ws.recv(decode=False))
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: received 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h; then sent 1011 (internal error) You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: h

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 22:38:49,389 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 22:38:49,393 - livekit.agents - DEBUG - session closed
2025-08-21 22:38:55,499 - livekit.agents - INFO - shutting down worker
2025-08-21 22:38:55,501 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:38:55,503 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 22:38:55,503 - livekit.agents - DEBUG - job exiting
2025-08-21 22:38:55,504 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 22:38:55,706 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-21 22:39:55,809 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:39:55,810 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:39:57,548 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:39:58,978 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:39:58,980 - livekit.agents - INFO - starting worker
2025-08-21 22:39:58,983 - livekit.agents - INFO - initializing job runner
2025-08-21 22:39:58,984 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:39:58,984 - livekit.agents - INFO - job runner initialized
2025-08-21 22:40:00,760 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:40:00,765 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:40:00,766 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:40:04,249 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:43:59,343 - livekit.agents - DEBUG - executing tool
2025-08-21 22:43:59,406 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:43:59,484 - livekit.agents - DEBUG - waiting for function call to finish before fully cancelling
2025-08-21 22:44:02,380 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:44:07,013 - livekit.plugins.google - WARNING - server cancelled tool calls
2025-08-21 22:44:07,018 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:44:18,300 - livekit.agents - DEBUG - executing tool
2025-08-21 22:44:18,302 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:44:18,329 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:44:25,128 - livekit.plugins.google - WARNING - server cancelled tool calls
2025-08-21 22:44:25,133 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:44:39,649 - livekit.agents - DEBUG - executing tool
2025-08-21 22:44:39,686 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:44:39,687 - livekit.agents - DEBUG - waiting for function call to finish before fully cancelling
2025-08-21 22:44:39,826 - livekit.plugins.google - WARNING - server cancelled tool calls
2025-08-21 22:44:39,830 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:44:41,503 - livekit.agents - DEBUG - executing tool
2025-08-21 22:44:41,533 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:44:41,534 - livekit.agents - DEBUG - waiting for function call to finish before fully cancelling
2025-08-21 22:44:42,268 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:44:45,035 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:44:46,196 - livekit.plugins.google - WARNING - server cancelled tool calls
2025-08-21 22:44:46,199 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:46:01,759 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:47:17,243 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:47:17,243 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:47:19,002 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:47:20,475 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:47:20,476 - livekit.agents - INFO - starting worker
2025-08-21 22:47:20,479 - livekit.agents - INFO - initializing job runner
2025-08-21 22:47:20,479 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:47:20,480 - livekit.agents - INFO - job runner initialized
2025-08-21 22:47:21,904 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:47:21,910 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:47:21,911 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:47:24,441 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:51:45,759 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:51:45,759 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:51:47,714 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:51:49,214 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:51:49,215 - livekit.agents - INFO - starting worker
2025-08-21 22:51:49,218 - livekit.agents - INFO - initializing job runner
2025-08-21 22:51:49,219 - livekit.agents - INFO - job runner initialized
2025-08-21 22:51:49,219 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:51:51,076 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:51:51,083 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:51:51,083 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:51:54,085 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:52:05,415 - livekit.agents - DEBUG - executing tool
2025-08-21 22:52:06,000 - livekit.agents - DEBUG - tools execution completed
2025-08-21 22:52:17,155 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:53:40,087 - livekit.plugins.google - ERROR - error in receive task: sent 1011 (internal error) keepalive ping timeout; no close frame received
Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 580, in finish_send
    return ov.getresult()
           ^^^^^^^^^^^^^^
OSError: [WinError 64] The specified network name is no longer available

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\proactor_events.py", line 385, in _loop_writing
    f.result()
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 854, in _poll
    value = callback(transferred, key, ov)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\asyncio\windows_events.py", line 584, in finish_send
    raise ConnectionResetError(*exc.args)
ConnectionResetError: [WinError 64] The specified network name is no longer available

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 701, in _recv_task
    async for response in session.receive():
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 443, in receive
    while result := await self._receive():
                    ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 524, in _receive
    raw_response = await self._ws.recv(decode=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\connection.py", line 279, in recv
    raise self.protocol.close_exc from self.recv_exc
websockets.exceptions.ConnectionClosedError: sent 1011 (internal error) keepalive ping timeout; no close frame received
2025-08-21 22:53:40,092 - livekit.plugins.google - DEBUG - send task finished.
2025-08-21 22:53:40,093 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:53:50,097 - livekit.plugins.google - ERROR - Gemini Realtime API error: timed out during handshake
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 22:53:50,142 - livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake
2025-08-21 22:53:50,148 - livekit.plugins.google - ERROR - Error in _main_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 589, in _main_task
    async with self._client.aio.live.connect(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\google\genai\live.py", line 1051, in connect
    async with ws_connect(
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 482, in __aenter__
    return await self
           ^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\websockets\asyncio\client.py", line 473, in __await_impl__
    raise TimeoutError("timed out during handshake") from None
TimeoutError: timed out during handshake

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\plugins\google\beta\realtime\realtime_api.py", line 639, in _main_task
    raise APIConnectionError(message="Failed to connect to Gemini Live") from e
livekit.agents._exceptions.APIConnectionError: Failed to connect to Gemini Live (body=None, retryable=True)
2025-08-21 22:53:50,156 - livekit.agents - DEBUG - session closed
2025-08-21 22:54:31,828 - livekit.agents - INFO - shutting down worker
2025-08-21 22:54:31,828 - livekit.agents - DEBUG - shutting down job task
2025-08-21 22:54:31,830 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-21 22:54:31,830 - livekit.agents - DEBUG - job exiting
2025-08-21 22:54:31,830 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-21 22:54:35,217 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-21 22:54:35,218 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-21 22:54:37,105 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-21 22:54:38,618 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:54:38,620 - livekit.agents - INFO - starting worker
2025-08-21 22:54:38,624 - livekit.agents - INFO - initializing job runner
2025-08-21 22:54:38,625 - livekit.agents - INFO - job runner initialized
2025-08-21 22:54:38,625 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-21 22:54:40,447 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-21 22:54:40,454 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:54:40,454 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-21 22:54:43,539 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-21 22:56:24,639 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-21 22:56:39,098 - livekit.agents - DEBUG - executing tool
2025-08-21 22:56:39,139 - livekit.agents - DEBUG - tools execution completed
2025-08-22 20:14:55,474 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-22 20:14:55,474 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-22 20:15:02,596 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-22 20:15:16,523 - __main__ - ERROR - ZARA startup error: cannot import name 'start_live_screen_monitoring' from 'tools' (C:\Users\<USER>\Desktop\New folder\ZARA\tools.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\New folder\ZARA\Zara_Voice_Assistant.py", line 39, in <module>
    from tools import (
ImportError: cannot import name 'start_live_screen_monitoring' from 'tools' (C:\Users\<USER>\Desktop\New folder\ZARA\tools.py)
2025-08-22 20:15:59,320 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-22 20:15:59,320 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-22 20:16:01,161 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-22 20:16:02,615 - __main__ - ERROR - ZARA startup error: cannot import name 'get_performance_stats' from 'tools' (C:\Users\<USER>\Desktop\New folder\ZARA\tools.py)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder\ZARA\run_zara.py", line 106, in main
    from Zara_Voice_Assistant import entrypoint
  File "C:\Users\<USER>\Desktop\New folder\ZARA\Zara_Voice_Assistant.py", line 39, in <module>
    from tools import (
ImportError: cannot import name 'get_performance_stats' from 'tools' (C:\Users\<USER>\Desktop\New folder\ZARA\tools.py)
2025-08-22 20:16:19,278 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-22 20:16:19,279 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-22 20:16:21,017 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-22 20:16:22,421 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 20:16:22,424 - livekit.agents - INFO - starting worker
2025-08-22 20:16:22,429 - livekit.agents - INFO - initializing job runner
2025-08-22 20:16:22,429 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 20:16:22,430 - livekit.agents - INFO - job runner initialized
2025-08-22 20:16:26,504 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\New folder\ZARA\Zara_Voice_Assistant.py", line 895, in entrypoint
    agent = Assistant()
            ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\New folder\ZARA\Zara_Voice_Assistant.py", line 184, in __init__
    start_live_screen_monitoring,
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'start_live_screen_monitoring' is not defined
2025-08-22 20:17:04,776 - livekit.agents - INFO - shutting down worker
2025-08-22 20:17:04,777 - livekit.agents - DEBUG - shutting down job task
2025-08-22 20:17:04,778 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-22 20:17:04,779 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-22 20:17:04,780 - livekit.agents - DEBUG - job exiting
2025-08-22 20:17:07,516 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-22 20:17:07,516 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-22 20:17:09,394 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-22 20:17:10,789 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 20:17:10,791 - livekit.agents - INFO - starting worker
2025-08-22 20:17:10,794 - livekit.agents - INFO - initializing job runner
2025-08-22 20:17:10,795 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 20:17:10,795 - livekit.agents - INFO - job runner initialized
2025-08-22 20:17:13,521 - livekit.agents - INFO - shutting down worker
2025-08-22 20:17:13,522 - livekit.agents - DEBUG - shutting down job task
2025-08-22 20:17:13,524 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-22 20:17:13,524 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-22 20:17:13,525 - livekit.agents - DEBUG - job exiting
2025-08-22 21:35:27,984 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-22 21:35:27,984 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-22 21:35:30,394 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-22 21:35:31,597 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 21:35:31,599 - livekit.agents - INFO - starting worker
2025-08-22 21:35:31,602 - livekit.agents - INFO - initializing job runner
2025-08-22 21:35:31,603 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 21:35:31,603 - livekit.agents - INFO - job runner initialized
2025-08-22 21:35:35,673 - livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "C:\Users\<USER>\Desktop\New folder\ZARA\Zara_Voice_Assistant.py", line 901, in entrypoint
    agent = Assistant()
            ^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\New folder\ZARA\Zara_Voice_Assistant.py", line 225, in __init__
    self._tool_patterns = self._initialize_tool_patterns()
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\New folder\ZARA\Zara_Voice_Assistant.py", line 349, in _initialize_tool_patterns
    'tool': get_weather,
            ^^^^^^^^^^^
NameError: name 'get_weather' is not defined
2025-08-22 21:36:44,933 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-22 21:36:44,933 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-22 21:36:47,164 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-22 21:36:48,283 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 21:36:48,285 - livekit.agents - INFO - starting worker
2025-08-22 21:36:48,287 - livekit.agents - INFO - initializing job runner
2025-08-22 21:36:48,288 - livekit.agents - INFO - job runner initialized
2025-08-22 21:36:48,288 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 21:38:14,501 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-22 21:38:14,502 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-22 21:38:16,717 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-22 21:38:17,802 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 21:38:17,804 - livekit.agents - INFO - starting worker
2025-08-22 21:38:17,807 - livekit.agents - INFO - initializing job runner
2025-08-22 21:38:17,808 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-22 21:38:17,808 - livekit.agents - INFO - job runner initialized
2025-08-22 21:38:20,691 - livekit.agents - WARNING - Running <Task finished name='Task-29' coro=<AgentSession._update_activity() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py:805> result=None> took too long: 2.06 seconds
2025-08-22 21:38:20,692 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-22 21:38:20,700 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-22 21:38:20,700 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-22 21:38:23,122 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-23 13:09:33,796 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-23 13:09:33,796 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-23 13:09:46,360 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-23 13:10:00,067 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:10:00,071 - livekit.agents - INFO - starting worker
2025-08-23 13:10:00,075 - livekit.agents - INFO - initializing job runner
2025-08-23 13:10:00,075 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:10:00,076 - livekit.agents - INFO - job runner initialized
2025-08-23 13:10:02,950 - livekit.agents - WARNING - Running <Task finished name='Task-29' coro=<AgentSession._update_activity() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_session.py:805> result=None> took too long: 2.06 seconds
2025-08-23 13:10:02,951 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-23 13:10:02,957 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:10:02,958 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:10:07,651 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-23 13:10:07,975 - livekit.agents - ERROR - Error in _realtime_reply_task
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-23 13:10:10,024 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.realtime_reply' coro=<AgentActivity._realtime_reply_task() done, defined at C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py:13> exception=RealtimeError('generate_reply timed out waiting for generation_created event.')>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\livekit\agents\voice\agent_activity.py", line 1752, in _realtime_reply_task
    generation_ev = await self._rt_session.generate_reply(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
livekit.agents.llm.realtime.RealtimeError: generate_reply timed out waiting for generation_created event.
2025-08-23 13:10:23,246 - livekit.agents - INFO - shutting down worker
2025-08-23 13:10:23,247 - livekit.agents - DEBUG - shutting down job task
2025-08-23 13:10:23,249 - livekit.plugins.google - DEBUG - send task finished.
2025-08-23 13:10:23,250 - livekit.agents - DEBUG - job exiting
2025-08-23 13:10:23,433 - livekit.agents - WARNING - exiting forcefully
2025-08-23 13:10:25,681 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-23 13:10:25,682 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-23 13:10:27,827 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-23 13:10:28,957 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:10:28,959 - livekit.agents - INFO - starting worker
2025-08-23 13:10:28,962 - livekit.agents - INFO - initializing job runner
2025-08-23 13:10:28,963 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:10:28,963 - livekit.agents - INFO - job runner initialized
2025-08-23 13:10:30,112 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-23 13:10:30,119 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:10:30,119 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:10:33,478 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-23 13:11:12,485 - livekit.agents - DEBUG - executing tool
2025-08-23 13:11:14,532 - livekit.agents - DEBUG - tools execution completed
2025-08-23 13:11:32,170 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-23 13:11:44,284 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-23 13:11:52,557 - livekit.agents - DEBUG - executing tool
2025-08-23 13:11:52,655 - livekit.agents - DEBUG - tools execution completed
2025-08-23 13:12:05,776 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-23 13:12:26,177 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-23 13:12:31,185 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-23 13:12:46,796 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-23 13:13:08,006 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-23 13:13:08,006 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-23 13:13:10,214 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-23 13:13:23,810 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-23 13:13:23,810 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-23 13:13:25,992 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-23 13:13:27,065 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:13:27,066 - livekit.agents - INFO - starting worker
2025-08-23 13:13:27,070 - livekit.agents - INFO - initializing job runner
2025-08-23 13:13:27,071 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:13:27,071 - livekit.agents - INFO - job runner initialized
2025-08-23 13:13:27,105 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:13:28,697 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-23 13:13:28,703 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:13:28,703 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:13:32,146 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-23 13:13:44,617 - livekit.agents - DEBUG - executing tool
2025-08-23 13:13:46,638 - livekit.agents - DEBUG - tools execution completed
2025-08-23 13:13:50,692 - livekit.agents - INFO - shutting down worker
2025-08-23 13:13:50,693 - livekit.agents - DEBUG - shutting down job task
2025-08-23 13:13:50,695 - livekit.agents - DEBUG - job exiting
2025-08-23 13:13:50,696 - livekit.plugins.google - DEBUG - send task finished.
2025-08-23 13:13:51,194 - livekit.agents - DEBUG - session closed
2025-08-23 13:13:51,196 - livekit.agents - DEBUG - http_session(): closing the httpclient ctx
2025-08-23 13:13:51,196 - livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
2025-08-23 13:13:57,569 - livekit.agents - WARNING - exiting forcefully
2025-08-23 13:13:59,885 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-23 13:13:59,885 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-23 13:14:02,302 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-23 13:14:03,495 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:14:03,497 - livekit.agents - INFO - starting worker
2025-08-23 13:14:03,502 - livekit.agents - INFO - initializing job runner
2025-08-23 13:14:03,502 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:14:03,503 - livekit.agents - INFO - job runner initialized
2025-08-23 13:14:03,538 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 13:14:04,802 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-23 13:14:04,808 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:14:04,808 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 13:14:08,091 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
2025-08-23 13:14:16,899 - livekit.agents - DEBUG - executing tool
2025-08-23 13:14:18,890 - livekit.agents - DEBUG - tools execution completed
2025-08-23 13:14:30,312 - livekit.agents - DEBUG - executing tool
2025-08-23 13:14:30,397 - livekit.agents - DEBUG - tools execution completed
2025-08-23 13:14:42,605 - livekit.plugins.google - WARNING - truncate is not supported by the Google Realtime API.
2025-08-23 13:15:07,251 - livekit.agents - DEBUG - executing tool
2025-08-23 13:15:07,912 - livekit.agents - DEBUG - tools execution completed
2025-08-23 13:15:40,752 - livekit.agents - DEBUG - executing tool
2025-08-23 13:15:40,831 - livekit.agents - DEBUG - tools execution completed
2025-08-23 13:15:53,021 - livekit.agents - DEBUG - executing tool
2025-08-23 13:15:53,687 - livekit.agents - DEBUG - tools execution completed
2025-08-23 16:28:35,142 - livekit - INFO - livekit_ffi::server:139:livekit_ffi::server - initializing ffi server v0.12.30
2025-08-23 16:28:35,143 - livekit - INFO - livekit_ffi::cabi:36:livekit_ffi::cabi - initializing ffi server v0.12.30
2025-08-23 16:28:38,069 - memory_system - INFO -  ZARA Memory System initialized successfully
2025-08-23 16:28:39,933 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 16:28:39,936 - livekit.agents - INFO - starting worker
2025-08-23 16:28:39,940 - livekit.agents - INFO - initializing job runner
2025-08-23 16:28:39,940 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 16:28:39,940 - livekit.agents - INFO - job runner initialized
2025-08-23 16:28:39,981 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-23 16:28:41,641 - livekit.plugins.google - DEBUG - connecting to Gemini Realtime API...
2025-08-23 16:28:41,649 - livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 16:28:41,649 - livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
2025-08-23 16:28:44,974 - google_genai.live - INFO - b'{\n  "setupComplete": {}\n}\n'
